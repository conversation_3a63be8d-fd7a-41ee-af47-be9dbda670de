package cathayfuture.opm.domain.system.repository;

import cathayfuture.opm.domain.system.SysRoleEntity;

import java.util.List;

/**
 * 系统角色Repository接口
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
public interface SysRoleRepository {

    /**
     * 根据角色编码查找角色
     *
     * @param roleCode 角色编码
     * @return 角色实体
     */
    SysRoleEntity findByRoleCode(String roleCode);

    /**
     * 根据ID查找角色
     *
     * @param id 角色ID
     * @return 角色实体
     */
    SysRoleEntity findById(Integer id);

    /**
     * 根据用户ID查找角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    List<SysRoleEntity> findByUserId(Integer userId);

    /**
     * 保存角色
     *
     * @param role 角色实体
     * @return 保存后的角色实体
     */
    SysRoleEntity save(SysRoleEntity role);

    /**
     * 根据ID更新角色
     *
     * @param role 角色实体
     * @return 更新结果
     */
    boolean updateById(SysRoleEntity role);

    /**
     * 根据ID删除角色（逻辑删除）
     *
     * @param id 角色ID
     * @return 删除结果
     */
    boolean deleteById(Integer id);

    /**
     * 查询所有启用的角色
     *
     * @return 角色列表
     */
    List<SysRoleEntity> findAllEnabled();

    /**
     * 检查角色编码是否存在
     *
     * @param roleCode 角色编码
     * @param excludeId 排除的角色ID（用于更新时检查）
     * @return 是否存在
     */
    boolean existsByRoleCode(String roleCode, Integer excludeId);

    /**
     * 根据角色ID列表查询角色
     *
     * @param roleIds 角色ID列表
     * @return 角色列表
     */
    List<SysRoleEntity> findByIds(List<Integer> roleIds);
}
