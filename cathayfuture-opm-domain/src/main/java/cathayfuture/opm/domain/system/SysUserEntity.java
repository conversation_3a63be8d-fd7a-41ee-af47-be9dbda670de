package cathayfuture.opm.domain.system;

import cathayfuture.opm.domain.order.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 系统用户实体
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_user")
public class SysUserEntity extends BaseEntity {

    /**
     * 用户名
     */
    @TableField("username")
    private String username;

    /**
     * 用户编码
     */
    @TableField("user_code")
    private String userCode;

    /**
     * 密码(BCrypt加密)
     */
    @TableField("password")
    private String password;

    /**
     * 真实姓名
     */
    @TableField("real_name")
    private String realName;

    /**
     * OA姓名
     */
    @TableField("oa_name")
    private String oaName;

    /**
     * 邮箱
     */
    @TableField("email")
    private String email;

    /**
     * 手机号
     */
    @TableField("phone")
    private String phone;

    /**
     * 头像URL
     */
    @TableField("avatar")
    private String avatar;

    /**
     * 状态(0:禁用 1:启用)
     */
    @TableField("status")
    private Integer status;

    /**
     * 最后登录时间
     */
    @TableField("last_login_time")
    private LocalDateTime lastLoginTime;

    /**
     * 最后登录IP
     */
    @TableField("last_login_ip")
    private String lastLoginIp;

    /**
     * 密码更新时间
     */
    @TableField("password_update_time")
    private LocalDateTime passwordUpdateTime;

    /**
     * 登录失败次数
     */
    @TableField("login_failure_count")
    private Integer loginFailureCount;

    /**
     * 账户锁定时间
     */
    @TableField("account_locked_time")
    private LocalDateTime accountLockedTime;

    /**
     * 用户状态枚举
     */
    public enum Status {
        DISABLED(0, "禁用"),
        ENABLED(1, "启用");

        private final Integer code;
        private final String desc;

        Status(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    /**
     * 检查用户是否启用
     */
    public boolean isEnabled() {
        return Status.ENABLED.getCode().equals(this.status);
    }

    /**
     * 检查账户是否被锁定
     */
    public boolean isAccountLocked() {
        return this.accountLockedTime != null && 
               this.accountLockedTime.isAfter(LocalDateTime.now());
    }

    /**
     * 检查是否需要重置密码（密码超过90天未更新）
     */
    public boolean isPasswordExpired() {
        if (this.passwordUpdateTime == null) {
            return false;
        }
        return this.passwordUpdateTime.isBefore(LocalDateTime.now().minusDays(90));
    }
}
