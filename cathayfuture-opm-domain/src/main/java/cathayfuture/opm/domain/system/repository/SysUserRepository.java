package cathayfuture.opm.domain.system.repository;

import cathayfuture.opm.domain.system.SysUserEntity;

import java.util.List;

/**
 * 系统用户Repository接口
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
public interface SysUserRepository {

    /**
     * 根据用户名查找用户
     *
     * @param username 用户名
     * @return 用户实体
     */
    SysUserEntity findByUsername(String username);

    /**
     * 根据用户编码查找用户
     *
     * @param userCode 用户编码
     * @return 用户实体
     */
    SysUserEntity findByUserCode(String userCode);

    /**
     * 根据邮箱查找用户
     *
     * @param email 邮箱
     * @return 用户实体
     */
    SysUserEntity findByEmail(String email);

    /**
     * 根据手机号查找用户
     *
     * @param phone 手机号
     * @return 用户实体
     */
    SysUserEntity findByPhone(String phone);

    /**
     * 根据ID查找用户
     *
     * @param id 用户ID
     * @return 用户实体
     */
    SysUserEntity findById(Integer id);

    /**
     * 保存用户
     *
     * @param user 用户实体
     * @return 保存后的用户实体
     */
    SysUserEntity save(SysUserEntity user);

    /**
     * 根据ID更新用户
     *
     * @param user 用户实体
     * @return 更新结果
     */
    boolean updateById(SysUserEntity user);

    /**
     * 根据ID删除用户（逻辑删除）
     *
     * @param id 用户ID
     * @return 删除结果
     */
    boolean deleteById(Integer id);

    /**
     * 根据条件查询用户列表
     *
     * @param username 用户名（模糊查询，可为空）
     * @param userCode 用户编码（模糊查询，可为空）
     * @param realName 真实姓名（模糊查询，可为空）
     * @param oaName OA姓名（模糊查询，可为空）
     * @param status 状态（可为空）
     * @return 用户列表
     */
    List<SysUserEntity> findByCondition(String username, String userCode, String realName, String oaName, Integer status);

    /**
     * 查询所有启用的用户
     *
     * @return 用户列表
     */
    List<SysUserEntity> findAllEnabled();

    /**
     * 检查用户名是否存在
     *
     * @param username 用户名
     * @param excludeId 排除的用户ID（用于更新时检查）
     * @return 是否存在
     */
    boolean existsByUsername(String username, Integer excludeId);

    /**
     * 检查用户编码是否存在
     *
     * @param userCode 用户编码
     * @param excludeId 排除的用户ID（用于更新时检查）
     * @return 是否存在
     */
    boolean existsByUserCode(String userCode, Integer excludeId);

    /**
     * 检查邮箱是否存在
     *
     * @param email 邮箱
     * @param excludeId 排除的用户ID（用于更新时检查）
     * @return 是否存在
     */
    boolean existsByEmail(String email, Integer excludeId);

    /**
     * 根据角色ID查询用户列表
     *
     * @param roleId 角色ID
     * @return 用户列表
     */
    List<SysUserEntity> findByRoleId(Integer roleId);
}
