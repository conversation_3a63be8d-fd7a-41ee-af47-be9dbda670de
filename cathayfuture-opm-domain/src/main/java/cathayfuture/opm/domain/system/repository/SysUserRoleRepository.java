package cathayfuture.opm.domain.system.repository;

import cathayfuture.opm.domain.system.SysUserRoleEntity;

import java.util.List;

/**
 * 用户角色关联Repository接口
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
public interface SysUserRoleRepository {

    /**
     * 根据用户ID查找用户角色关联
     *
     * @param userId 用户ID
     * @return 用户角色关联列表
     */
    List<SysUserRoleEntity> findByUserId(Integer userId);

    /**
     * 根据角色ID查找用户角色关联
     *
     * @param roleId 角色ID
     * @return 用户角色关联列表
     */
    List<SysUserRoleEntity> findByRoleId(Integer roleId);

    /**
     * 保存用户角色关联
     *
     * @param userRole 用户角色关联实体
     * @return 保存后的实体
     */
    SysUserRoleEntity save(SysUserRoleEntity userRole);

    /**
     * 批量保存用户角色关联
     *
     * @param userRoles 用户角色关联列表
     * @return 保存结果
     */
    boolean saveBatch(List<SysUserRoleEntity> userRoles);

    /**
     * 根据用户ID删除用户角色关联
     *
     * @param userId 用户ID
     * @return 删除结果
     */
    boolean deleteByUserId(Integer userId);

    /**
     * 根据角色ID删除用户角色关联
     *
     * @param roleId 角色ID
     * @return 删除结果
     */
    boolean deleteByRoleId(Integer roleId);

    /**
     * 删除指定的用户角色关联
     *
     * @param userId 用户ID
     * @param roleId 角色ID
     * @return 删除结果
     */
    boolean deleteByUserIdAndRoleId(Integer userId, Integer roleId);

    /**
     * 检查用户角色关联是否存在
     *
     * @param userId 用户ID
     * @param roleId 角色ID
     * @return 是否存在
     */
    boolean existsByUserIdAndRoleId(Integer userId, Integer roleId);

    /**
     * 为用户分配角色（先删除原有关联，再添加新关联）
     *
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     * @return 分配结果
     */
    boolean assignRolesToUser(Integer userId, List<Integer> roleIds);
}
