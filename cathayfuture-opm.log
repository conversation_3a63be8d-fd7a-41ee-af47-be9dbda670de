2025-08-12 10:26:26,319 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App on chengzhx-MBP with PID 40199 (started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来)
2025-08-12 10:26:26,322 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: test
2025-08-12 10:26:26,983 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-12 10:26:26,985 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-12 10:26:27,030 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 39ms. Found 0 repository interfaces.
2025-08-12 10:26:27,285 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$2340077c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-12 10:26:27,410 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-12 10:26:27,417 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-12 10:26:27,422 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-12 10:26:27,422 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-12 10:26:27,473 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-12 10:26:27,473 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 1130 ms
2025-08-12 10:26:28,171 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.redisson.Version [L:41] - Redisson 3.10.5
2025-08-12 10:26:28,283 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-7] o.r.c.p.MasterPubSubConnectionPool [L:168] - 1 connections initialized for *********/*********:6379
2025-08-12 10:26:28,283 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-6] o.r.c.pool.MasterConnectionPool [L:168] - 1 connections initialized for *********/*********:6379
2025-08-12 10:26:28,822 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.reflections.Reflections [L:232] - Reflections took 42 ms to scan 5 urls, producing 120 keys and 409 values 
2025-08-12 10:26:29,003 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.web.DefaultSecurityFilterChain [L:43] - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@62de73eb, org.springframework.security.web.context.SecurityContextPersistenceFilter@1ff463bb, org.springframework.security.web.header.HeaderWriterFilter@5b3755f4, org.springframework.security.web.authentication.logout.LogoutFilter@56cd5d76, cathayfuture.opm.adapter.security.JwtAuthenticationFilter@7bc6935c, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7769d9b6, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@315cf170, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@97b5e6a, org.springframework.security.web.session.SessionManagementFilter@fd87c22, org.springframework.security.web.access.ExceptionTranslationFilter@56478522, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@5fc1e4fb]
2025-08-12 10:26:29,090 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.c.ThreadPoolTaskExecutor [L:171] - Initializing ExecutorService 'applicationTaskExecutor'
2025-08-12 10:26:29,345 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.e.web.EndpointLinksResolver [L:59] - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-08-12 10:26:29,405 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Starting ProtocolHandler ["http-nio-8080"]
2025-08-12 10:26:29,411 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:204] - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-12 10:26:29,413 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:59] - Started App in 3.406 seconds (JVM running for 3.734)
2025-08-12 10:26:29,529 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(2)-*************] com.zaxxer.hikari.HikariDataSource [L:110] - HikariPool-1 - Starting...
2025-08-12 10:26:29,530 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(4)-*************] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-12 10:26:29,531 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(4)-*************] o.s.web.servlet.DispatcherServlet [L:524] - Initializing Servlet 'dispatcherServlet'
2025-08-12 10:26:29,537 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(4)-*************] o.s.web.servlet.DispatcherServlet [L:546] - Completed initialization in 6 ms
2025-08-12 10:26:29,638 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(2)-*************] com.zaxxer.hikari.HikariDataSource [L:123] - HikariPool-1 - Start completed.
2025-08-12 10:26:46,610 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] o.s.api.AbstractOpenApiResource [L:347] - Init duration for springdoc-openapi is: 647 ms
2025-08-12 10:28:08,086 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-8] o.s.s.c.ThreadPoolTaskExecutor [L:208] - Shutting down ExecutorService 'applicationTaskExecutor'
2025-08-12 10:28:08,109 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-8] com.zaxxer.hikari.HikariDataSource [L:350] - HikariPool-1 - Shutdown initiated...
2025-08-12 10:28:08,111 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-8] com.zaxxer.hikari.HikariDataSource [L:352] - HikariPool-1 - Shutdown completed.
2025-08-12 10:42:24,863 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App on chengzhx-MBP with PID 41061 (started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来)
2025-08-12 10:42:24,865 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: test
2025-08-12 10:42:25,587 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-12 10:42:25,588 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-12 10:42:25,625 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 30ms. Found 0 repository interfaces.
2025-08-12 10:42:25,877 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$a61ea0c9] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-12 10:42:26,008 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-12 10:42:26,017 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-12 10:42:26,022 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-12 10:42:26,023 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-12 10:42:26,079 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-12 10:42:26,079 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 1195 ms
2025-08-12 10:42:26,780 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.redisson.Version [L:41] - Redisson 3.10.5
2025-08-12 10:42:26,893 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-6] o.r.c.pool.MasterConnectionPool [L:168] - 1 connections initialized for *********/*********:6379
2025-08-12 10:42:26,893 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-7] o.r.c.p.MasterPubSubConnectionPool [L:168] - 1 connections initialized for *********/*********:6379
2025-08-12 10:42:27,432 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.reflections.Reflections [L:232] - Reflections took 42 ms to scan 5 urls, producing 120 keys and 409 values 
2025-08-12 10:42:27,621 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.web.DefaultSecurityFilterChain [L:43] - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3a8fb023, org.springframework.security.web.context.SecurityContextPersistenceFilter@225e09f0, org.springframework.security.web.header.HeaderWriterFilter@7c956dda, org.springframework.security.web.authentication.logout.LogoutFilter@fd87c22, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2fa212df, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@59f7c106, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@26d73519, org.springframework.security.web.session.SessionManagementFilter@3d132bb6, org.springframework.security.web.access.ExceptionTranslationFilter@986b619, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@165aa43a]
2025-08-12 10:42:27,709 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.c.ThreadPoolTaskExecutor [L:171] - Initializing ExecutorService 'applicationTaskExecutor'
2025-08-12 10:42:27,965 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.e.web.EndpointLinksResolver [L:59] - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-08-12 10:42:28,026 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Starting ProtocolHandler ["http-nio-8080"]
2025-08-12 10:42:28,033 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:204] - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-12 10:42:28,035 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:59] - Started App in 3.488 seconds (JVM running for 3.859)
2025-08-12 10:42:28,420 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(3)-*************] com.zaxxer.hikari.HikariDataSource [L:110] - HikariPool-1 - Starting...
2025-08-12 10:42:28,421 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(2)-*************] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-12 10:42:28,421 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(2)-*************] o.s.web.servlet.DispatcherServlet [L:524] - Initializing Servlet 'dispatcherServlet'
2025-08-12 10:42:28,428 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(2)-*************] o.s.web.servlet.DispatcherServlet [L:546] - Completed initialization in 6 ms
2025-08-12 10:42:28,531 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(3)-*************] com.zaxxer.hikari.HikariDataSource [L:123] - HikariPool-1 - Start completed.
2025-08-12 10:46:15,083 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-9] o.s.s.c.ThreadPoolTaskExecutor [L:208] - Shutting down ExecutorService 'applicationTaskExecutor'
2025-08-12 10:46:15,109 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-9] com.zaxxer.hikari.HikariDataSource [L:350] - HikariPool-1 - Shutdown initiated...
2025-08-12 10:46:15,113 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-9] com.zaxxer.hikari.HikariDataSource [L:352] - HikariPool-1 - Shutdown completed.
2025-08-12 10:46:16,617 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App on chengzhx-MBP with PID 41263 (started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来)
2025-08-12 10:46:16,619 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: test
2025-08-12 10:46:17,337 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-12 10:46:17,338 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-12 10:46:17,373 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 30ms. Found 0 repository interfaces.
2025-08-12 10:46:17,618 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$8eae4fcb] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-12 10:46:17,739 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-12 10:46:17,746 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-12 10:46:17,751 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-12 10:46:17,751 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-12 10:46:17,809 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-12 10:46:17,809 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 1170 ms
2025-08-12 10:46:18,506 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.redisson.Version [L:41] - Redisson 3.10.5
2025-08-12 10:46:18,616 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-6] o.r.c.pool.MasterConnectionPool [L:168] - 1 connections initialized for *********/*********:6379
2025-08-12 10:46:18,616 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-7] o.r.c.p.MasterPubSubConnectionPool [L:168] - 1 connections initialized for *********/*********:6379
2025-08-12 10:46:19,141 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.reflections.Reflections [L:232] - Reflections took 42 ms to scan 5 urls, producing 120 keys and 409 values 
2025-08-12 10:46:19,325 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.web.DefaultSecurityFilterChain [L:43] - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4468fda8, org.springframework.security.web.context.SecurityContextPersistenceFilter@37d3aa8c, org.springframework.security.web.header.HeaderWriterFilter@6e3b2dd3, org.springframework.security.web.authentication.logout.LogoutFilter@563263a, cathayfuture.opm.adapter.security.JwtAuthenticationFilter@7f287b98, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1bdafb01, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1ff463bb, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5fc1e4fb, org.springframework.security.web.session.SessionManagementFilter@3caf5c96, org.springframework.security.web.access.ExceptionTranslationFilter@2fa212df, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6cee903a]
2025-08-12 10:46:19,413 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.c.ThreadPoolTaskExecutor [L:171] - Initializing ExecutorService 'applicationTaskExecutor'
2025-08-12 10:46:19,674 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.e.web.EndpointLinksResolver [L:59] - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-08-12 10:46:19,735 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Starting ProtocolHandler ["http-nio-8080"]
2025-08-12 10:46:19,742 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:204] - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-12 10:46:19,743 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:59] - Started App in 3.448 seconds (JVM running for 3.871)
2025-08-12 10:46:20,092 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(1)-*************] com.zaxxer.hikari.HikariDataSource [L:110] - HikariPool-1 - Starting...
2025-08-12 10:46:20,093 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(4)-*************] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-12 10:46:20,093 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(4)-*************] o.s.web.servlet.DispatcherServlet [L:524] - Initializing Servlet 'dispatcherServlet'
2025-08-12 10:46:20,099 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(4)-*************] o.s.web.servlet.DispatcherServlet [L:546] - Completed initialization in 6 ms
2025-08-12 10:46:20,193 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(1)-*************] com.zaxxer.hikari.HikariDataSource [L:123] - HikariPool-1 - Start completed.
2025-08-12 10:46:39,191 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] o.s.api.AbstractOpenApiResource [L:347] - Init duration for springdoc-openapi is: 670 ms
2025-08-12 10:46:50,114 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-9] o.s.s.c.ThreadPoolTaskExecutor [L:208] - Shutting down ExecutorService 'applicationTaskExecutor'
2025-08-12 10:46:50,132 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-9] com.zaxxer.hikari.HikariDataSource [L:350] - HikariPool-1 - Shutdown initiated...
2025-08-12 10:46:50,135 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-9] com.zaxxer.hikari.HikariDataSource [L:352] - HikariPool-1 - Shutdown completed.
2025-08-12 10:46:58,231 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App on chengzhx-MBP with PID 41318 (started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来)
2025-08-12 10:46:58,233 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: test
2025-08-12 10:46:58,917 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-12 10:46:58,918 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-12 10:46:58,953 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 30ms. Found 0 repository interfaces.
2025-08-12 10:46:59,200 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$a32e7242] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-12 10:46:59,316 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-12 10:46:59,325 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-12 10:46:59,330 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-12 10:46:59,330 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-12 10:46:59,389 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-12 10:46:59,389 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 1137 ms
2025-08-12 10:47:00,074 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.redisson.Version [L:41] - Redisson 3.10.5
2025-08-12 10:47:00,182 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-6] o.r.c.p.MasterPubSubConnectionPool [L:168] - 1 connections initialized for *********/*********:6379
2025-08-12 10:47:00,182 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-7] o.r.c.pool.MasterConnectionPool [L:168] - 1 connections initialized for *********/*********:6379
2025-08-12 10:47:00,710 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.reflections.Reflections [L:232] - Reflections took 41 ms to scan 5 urls, producing 120 keys and 409 values 
2025-08-12 10:47:00,891 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.web.DefaultSecurityFilterChain [L:43] - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@165aa43a, org.springframework.security.web.context.SecurityContextPersistenceFilter@24eecabf, org.springframework.security.web.header.HeaderWriterFilter@1b8d20e6, org.springframework.security.web.authentication.logout.LogoutFilter@5b3755f4, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@78d6c244, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@bc8d68b, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@22cd8ec2, org.springframework.security.web.session.SessionManagementFilter@7c956dda, org.springframework.security.web.access.ExceptionTranslationFilter@1a43a88e, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@37cf91d8]
2025-08-12 10:47:00,978 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.c.ThreadPoolTaskExecutor [L:171] - Initializing ExecutorService 'applicationTaskExecutor'
2025-08-12 10:47:01,239 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.e.web.EndpointLinksResolver [L:59] - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-08-12 10:47:01,300 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Starting ProtocolHandler ["http-nio-8080"]
2025-08-12 10:47:01,307 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:204] - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-12 10:47:01,308 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:59] - Started App in 3.401 seconds (JVM running for 3.693)
2025-08-12 10:47:01,848 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(3)-*************] com.zaxxer.hikari.HikariDataSource [L:110] - HikariPool-1 - Starting...
2025-08-12 10:47:01,848 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(2)-*************] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-12 10:47:01,848 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(2)-*************] o.s.web.servlet.DispatcherServlet [L:524] - Initializing Servlet 'dispatcherServlet'
2025-08-12 10:47:01,854 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(2)-*************] o.s.web.servlet.DispatcherServlet [L:546] - Completed initialization in 5 ms
2025-08-12 10:47:01,958 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(3)-*************] com.zaxxer.hikari.HikariDataSource [L:123] - HikariPool-1 - Start completed.
2025-08-12 10:47:09,342 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-9] o.s.s.c.ThreadPoolTaskExecutor [L:208] - Shutting down ExecutorService 'applicationTaskExecutor'
2025-08-12 10:47:09,370 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-9] com.zaxxer.hikari.HikariDataSource [L:350] - HikariPool-1 - Shutdown initiated...
2025-08-12 10:47:09,374 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-9] com.zaxxer.hikari.HikariDataSource [L:352] - HikariPool-1 - Shutdown completed.
2025-08-12 14:29:11,096 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App on chengzhx-MBP with PID 58863 (started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来)
2025-08-12 14:29:11,098 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: test
2025-08-12 14:29:11,881 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-12 14:29:11,882 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-12 14:29:11,924 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 33ms. Found 0 repository interfaces.
2025-08-12 14:29:12,189 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$220a20a1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-12 14:29:12,334 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-12 14:29:12,342 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-12 14:29:12,348 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-12 14:29:12,348 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-12 14:29:12,407 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-12 14:29:12,407 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 1288 ms
2025-08-12 14:29:13,162 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.redisson.Version [L:41] - Redisson 3.10.5
2025-08-12 14:29:13,291 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-7] o.r.c.p.MasterPubSubConnectionPool [L:168] - 1 connections initialized for *********/*********:6379
2025-08-12 14:29:13,291 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-6] o.r.c.pool.MasterConnectionPool [L:168] - 1 connections initialized for *********/*********:6379
2025-08-12 14:29:13,957 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.reflections.Reflections [L:232] - Reflections took 47 ms to scan 5 urls, producing 126 keys and 452 values 
2025-08-12 14:29:14,190 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.web.DefaultSecurityFilterChain [L:43] - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@41059616, org.springframework.security.web.context.SecurityContextPersistenceFilter@40538370, org.springframework.security.web.header.HeaderWriterFilter@22cd8ec2, org.springframework.security.web.authentication.logout.LogoutFilter@47162173, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@18faaff6, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4ca970d5, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2e93108a, org.springframework.security.web.session.SessionManagementFilter@26d73519, org.springframework.security.web.access.ExceptionTranslationFilter@629cbb1, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@4348fa35]
2025-08-12 14:29:14,284 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.c.ThreadPoolTaskExecutor [L:171] - Initializing ExecutorService 'applicationTaskExecutor'
2025-08-12 14:29:14,570 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.e.web.EndpointLinksResolver [L:59] - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-08-12 14:29:14,641 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Starting ProtocolHandler ["http-nio-8080"]
2025-08-12 14:29:14,649 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:204] - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-12 14:29:14,651 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:59] - Started App in 3.894 seconds (JVM running for 4.233)
2025-08-12 14:29:15,178 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(3)-*************] com.zaxxer.hikari.HikariDataSource [L:110] - HikariPool-1 - Starting...
2025-08-12 14:29:15,186 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(4)-*************] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-12 14:29:15,191 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(4)-*************] o.s.web.servlet.DispatcherServlet [L:524] - Initializing Servlet 'dispatcherServlet'
2025-08-12 14:29:15,205 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(4)-*************] o.s.web.servlet.DispatcherServlet [L:546] - Completed initialization in 13 ms
2025-08-12 14:29:15,318 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(3)-*************] com.zaxxer.hikari.HikariDataSource [L:123] - HikariPool-1 - Start completed.
2025-08-12 14:32:47,147 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] o.s.api.AbstractOpenApiResource [L:347] - Init duration for springdoc-openapi is: 750 ms
2025-08-12 14:37:18,018 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.s.SysUserAppServiceImpl [L:50] - 创建用户: testuser
2025-08-12 14:37:18,098 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.i.c.BaseEntityMetaObjectHandler [L:22] - start insert fill ....
2025-08-12 14:37:18,099 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.i.c.BaseEntityMetaObjectHandler [L:24] - tenantId is [1]
2025-08-12 14:37:18,100 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.i.c.BaseEntityMetaObjectHandler [L:26] - instanceId is [1]
2025-08-12 14:37:18,101 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.i.c.BaseEntityMetaObjectHandler [L:28] - createPerson is [null]
2025-08-12 14:37:18,101 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.i.c.BaseEntityMetaObjectHandler [L:31] - updatePerson is [null]
2025-08-12 14:37:18,107 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.s.SysUserAppServiceImpl [L:84] - 用户创建成功: id=3, username=testuser
2025-08-12 14:39:48,910 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.s.SysUserAppServiceImpl [L:50] - 创建用户: debuguser
2025-08-12 14:39:48,983 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.i.c.BaseEntityMetaObjectHandler [L:22] - start insert fill ....
2025-08-12 14:39:48,983 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.i.c.BaseEntityMetaObjectHandler [L:24] - tenantId is [1]
2025-08-12 14:39:48,984 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.i.c.BaseEntityMetaObjectHandler [L:26] - instanceId is [1]
2025-08-12 14:39:48,984 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.i.c.BaseEntityMetaObjectHandler [L:28] - createPerson is [null]
2025-08-12 14:39:48,984 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.i.c.BaseEntityMetaObjectHandler [L:31] - updatePerson is [null]
2025-08-12 14:39:48,988 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.s.SysUserAppServiceImpl [L:84] - 用户创建成功: id=4, username=debuguser
2025-08-12 14:39:49,115 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.s.SysUserAppServiceImpl [L:91] - 更新用户: id=1
2025-08-12 14:39:49,123 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.i.c.BaseEntityMetaObjectHandler [L:38] - updatePerson is [null]
2025-08-12 14:39:49,127 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.s.SysUserAppServiceImpl [L:133] - 用户更新成功: id=1, username=admin
2025-08-12 14:40:53,233 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.s.SysUserAppServiceImpl [L:50] - 创建用户: springtest
2025-08-12 14:40:53,307 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.i.c.BaseEntityMetaObjectHandler [L:22] - start insert fill ....
2025-08-12 14:40:53,307 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.i.c.BaseEntityMetaObjectHandler [L:24] - tenantId is [1]
2025-08-12 14:40:53,307 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.i.c.BaseEntityMetaObjectHandler [L:26] - instanceId is [1]
2025-08-12 14:40:53,307 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.i.c.BaseEntityMetaObjectHandler [L:28] - createPerson is [null]
2025-08-12 14:40:53,307 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.i.c.BaseEntityMetaObjectHandler [L:31] - updatePerson is [null]
2025-08-12 14:40:53,312 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.s.SysUserAppServiceImpl [L:84] - 用户创建成功: id=5, username=springtest
2025-08-12 14:40:53,405 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.s.SysUserAppServiceImpl [L:50] - 创建用户: tempadmin
2025-08-12 14:40:53,477 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.i.c.BaseEntityMetaObjectHandler [L:22] - start insert fill ....
2025-08-12 14:40:53,477 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.i.c.BaseEntityMetaObjectHandler [L:24] - tenantId is [1]
2025-08-12 14:40:53,477 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.i.c.BaseEntityMetaObjectHandler [L:26] - instanceId is [1]
2025-08-12 14:40:53,478 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.i.c.BaseEntityMetaObjectHandler [L:28] - createPerson is [null]
2025-08-12 14:40:53,478 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.i.c.BaseEntityMetaObjectHandler [L:31] - updatePerson is [null]
2025-08-12 14:40:53,482 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.s.SysUserAppServiceImpl [L:84] - 用户创建成功: id=6, username=tempadmin
2025-08-12 14:40:53,534 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.s.SysUserAppServiceImpl [L:91] - 更新用户: id=1
2025-08-12 14:40:53,538 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.i.c.BaseEntityMetaObjectHandler [L:38] - updatePerson is [null]
2025-08-12 14:40:53,546 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.i.c.BaseEntityMetaObjectHandler [L:38] - updatePerson is [null]
2025-08-12 14:40:53,550 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.i.c.BaseEntityMetaObjectHandler [L:22] - start insert fill ....
2025-08-12 14:40:53,551 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.i.c.BaseEntityMetaObjectHandler [L:24] - tenantId is [1]
2025-08-12 14:40:53,551 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.i.c.BaseEntityMetaObjectHandler [L:26] - instanceId is [1]
2025-08-12 14:40:53,551 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.i.c.BaseEntityMetaObjectHandler [L:28] - createPerson is [null]
2025-08-12 14:40:53,551 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.i.c.BaseEntityMetaObjectHandler [L:31] - updatePerson is [null]
2025-08-12 14:40:53,586 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.s.SysUserAppServiceImpl [L:133] - 用户更新成功: id=1, username=admin
2025-08-12 14:41:51,619 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.s.SysUserAppServiceImpl [L:50] - 创建用户: temp_admin
2025-08-12 14:41:51,694 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.i.c.BaseEntityMetaObjectHandler [L:22] - start insert fill ....
2025-08-12 14:41:51,694 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.i.c.BaseEntityMetaObjectHandler [L:24] - tenantId is [1]
2025-08-12 14:41:51,695 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.i.c.BaseEntityMetaObjectHandler [L:26] - instanceId is [1]
2025-08-12 14:41:51,695 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.i.c.BaseEntityMetaObjectHandler [L:28] - createPerson is [null]
2025-08-12 14:41:51,695 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.i.c.BaseEntityMetaObjectHandler [L:31] - updatePerson is [null]
2025-08-12 14:41:51,699 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.s.SysUserAppServiceImpl [L:84] - 用户创建成功: id=7, username=temp_admin
2025-08-12 14:41:51,738 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.s.SysUserAppServiceImpl [L:91] - 更新用户: id=1
2025-08-12 14:41:51,741 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.i.c.BaseEntityMetaObjectHandler [L:38] - updatePerson is [null]
2025-08-12 14:41:51,746 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.i.c.BaseEntityMetaObjectHandler [L:38] - updatePerson is [null]
2025-08-12 14:41:51,750 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.i.c.BaseEntityMetaObjectHandler [L:22] - start insert fill ....
2025-08-12 14:41:51,750 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.i.c.BaseEntityMetaObjectHandler [L:24] - tenantId is [1]
2025-08-12 14:41:51,750 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.i.c.BaseEntityMetaObjectHandler [L:26] - instanceId is [1]
2025-08-12 14:41:51,750 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.i.c.BaseEntityMetaObjectHandler [L:28] - createPerson is [null]
2025-08-12 14:41:51,751 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.i.c.BaseEntityMetaObjectHandler [L:31] - updatePerson is [null]
2025-08-12 14:41:51,754 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.s.SysUserAppServiceImpl [L:133] - 用户更新成功: id=1, username=admin
2025-08-12 14:41:51,784 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.s.SysUserAppServiceImpl [L:140] - 删除用户: id=7
2025-08-12 14:41:51,788 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.i.c.BaseEntityMetaObjectHandler [L:38] - updatePerson is [null]
2025-08-12 14:41:51,791 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.i.c.BaseEntityMetaObjectHandler [L:38] - updatePerson is [null]
2025-08-12 14:41:51,796 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.s.SysUserAppServiceImpl [L:154] - 用户删除成功: id=7, username=temp_admin
2025-08-12 14:41:51,807 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.s.SysUserAppServiceImpl [L:50] - 创建用户: temp_manager
2025-08-12 14:41:51,880 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.i.c.BaseEntityMetaObjectHandler [L:22] - start insert fill ....
2025-08-12 14:41:51,880 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.i.c.BaseEntityMetaObjectHandler [L:24] - tenantId is [1]
2025-08-12 14:41:51,881 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.i.c.BaseEntityMetaObjectHandler [L:26] - instanceId is [1]
2025-08-12 14:41:51,881 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.i.c.BaseEntityMetaObjectHandler [L:28] - createPerson is [null]
2025-08-12 14:41:51,881 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.i.c.BaseEntityMetaObjectHandler [L:31] - updatePerson is [null]
2025-08-12 14:41:51,890 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.s.SysUserAppServiceImpl [L:84] - 用户创建成功: id=8, username=temp_manager
2025-08-12 14:41:51,941 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.s.SysUserAppServiceImpl [L:91] - 更新用户: id=2
2025-08-12 14:41:51,944 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.i.c.BaseEntityMetaObjectHandler [L:38] - updatePerson is [null]
2025-08-12 14:41:51,947 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.i.c.BaseEntityMetaObjectHandler [L:38] - updatePerson is [null]
2025-08-12 14:41:51,952 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.i.c.BaseEntityMetaObjectHandler [L:22] - start insert fill ....
2025-08-12 14:41:51,953 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.i.c.BaseEntityMetaObjectHandler [L:24] - tenantId is [1]
2025-08-12 14:41:51,953 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.i.c.BaseEntityMetaObjectHandler [L:26] - instanceId is [1]
2025-08-12 14:41:51,953 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.i.c.BaseEntityMetaObjectHandler [L:28] - createPerson is [null]
2025-08-12 14:41:51,953 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.i.c.BaseEntityMetaObjectHandler [L:31] - updatePerson is [null]
2025-08-12 14:41:51,960 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.s.SysUserAppServiceImpl [L:133] - 用户更新成功: id=2, username=manager
2025-08-12 14:41:51,984 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.s.SysUserAppServiceImpl [L:140] - 删除用户: id=8
2025-08-12 14:41:51,989 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.i.c.BaseEntityMetaObjectHandler [L:38] - updatePerson is [null]
2025-08-12 14:41:51,992 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.i.c.BaseEntityMetaObjectHandler [L:38] - updatePerson is [null]
2025-08-12 14:41:51,995 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.s.SysUserAppServiceImpl [L:154] - 用户删除成功: id=8, username=temp_manager
2025-08-12 14:43:05,215 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.s.SysUserAppServiceImpl [L:50] - 创建用户: adminclone
2025-08-12 14:43:05,287 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.i.c.BaseEntityMetaObjectHandler [L:22] - start insert fill ....
2025-08-12 14:43:05,287 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.i.c.BaseEntityMetaObjectHandler [L:24] - tenantId is [1]
2025-08-12 14:43:05,287 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.i.c.BaseEntityMetaObjectHandler [L:26] - instanceId is [1]
2025-08-12 14:43:05,288 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.i.c.BaseEntityMetaObjectHandler [L:28] - createPerson is [null]
2025-08-12 14:43:05,288 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.i.c.BaseEntityMetaObjectHandler [L:31] - updatePerson is [null]
2025-08-12 14:43:05,295 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.i.c.BaseEntityMetaObjectHandler [L:38] - updatePerson is [null]
2025-08-12 14:43:05,298 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.i.c.BaseEntityMetaObjectHandler [L:22] - start insert fill ....
2025-08-12 14:43:05,298 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.i.c.BaseEntityMetaObjectHandler [L:24] - tenantId is [1]
2025-08-12 14:43:05,298 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.i.c.BaseEntityMetaObjectHandler [L:26] - instanceId is [1]
2025-08-12 14:43:05,298 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.i.c.BaseEntityMetaObjectHandler [L:28] - createPerson is [null]
2025-08-12 14:43:05,298 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.i.c.BaseEntityMetaObjectHandler [L:31] - updatePerson is [null]
2025-08-12 14:43:05,301 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.s.SysUserAppServiceImpl [L:84] - 用户创建成功: id=9, username=adminclone
2025-08-12 14:44:08,799 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.s.SysUserAppServiceImpl [L:140] - 删除用户: id=1
2025-08-12 14:44:08,804 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.i.c.BaseEntityMetaObjectHandler [L:38] - updatePerson is [null]
2025-08-12 14:44:08,807 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.i.c.BaseEntityMetaObjectHandler [L:38] - updatePerson is [null]
2025-08-12 14:44:08,810 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.s.SysUserAppServiceImpl [L:154] - 用户删除成功: id=1, username=admin
2025-08-12 14:44:08,823 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.s.SysUserAppServiceImpl [L:50] - 创建用户: admin
2025-08-12 14:44:08,859 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.s.SysUserAppServiceImpl [L:140] - 删除用户: id=2
2025-08-12 14:44:08,863 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.i.c.BaseEntityMetaObjectHandler [L:38] - updatePerson is [null]
2025-08-12 14:44:08,868 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.i.c.BaseEntityMetaObjectHandler [L:38] - updatePerson is [null]
2025-08-12 14:44:08,871 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.s.SysUserAppServiceImpl [L:154] - 用户删除成功: id=2, username=manager
2025-08-12 14:44:08,881 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.s.SysUserAppServiceImpl [L:50] - 创建用户: manager
2025-08-12 14:44:25,539 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-10] o.s.s.c.ThreadPoolTaskExecutor [L:208] - Shutting down ExecutorService 'applicationTaskExecutor'
2025-08-12 14:44:25,562 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-10] com.zaxxer.hikari.HikariDataSource [L:350] - HikariPool-1 - Shutdown initiated...
2025-08-12 14:44:25,564 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-10] com.zaxxer.hikari.HikariDataSource [L:352] - HikariPool-1 - Shutdown completed.
2025-08-12 14:56:29,386 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App on chengzhx-MBP with PID 60387 (started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来)
2025-08-12 14:56:29,389 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: test
2025-08-12 14:56:30,097 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-12 14:56:30,098 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-12 14:56:30,136 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 32ms. Found 0 repository interfaces.
2025-08-12 14:56:30,402 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$27e1ce21] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-12 14:56:30,532 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-12 14:56:30,539 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-12 14:56:30,544 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-12 14:56:30,544 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-12 14:56:30,601 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-12 14:56:30,601 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 1192 ms
2025-08-12 14:56:31,327 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.redisson.Version [L:41] - Redisson 3.10.5
2025-08-12 14:56:31,447 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-7] o.r.c.pool.MasterConnectionPool [L:168] - 1 connections initialized for *********/*********:6379
2025-08-12 14:56:31,447 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-6] o.r.c.p.MasterPubSubConnectionPool [L:168] - 1 connections initialized for *********/*********:6379
2025-08-12 14:56:32,041 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.reflections.Reflections [L:232] - Reflections took 44 ms to scan 5 urls, producing 127 keys and 454 values 
2025-08-12 14:56:32,129 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Stopping service [Tomcat]
2025-08-12 14:56:32,138 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.l.ConditionEvaluationReportLoggingListener [L:142] - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-12 14:56:57,205 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App on chengzhx-MBP with PID 60412 (started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来)
2025-08-12 14:56:57,207 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: test
2025-08-12 14:56:57,917 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-12 14:56:57,918 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-12 14:56:57,957 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 33ms. Found 0 repository interfaces.
2025-08-12 14:56:58,218 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$6906433d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-12 14:56:58,345 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-12 14:56:58,353 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-12 14:56:58,358 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-12 14:56:58,359 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-12 14:56:58,417 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-12 14:56:58,417 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 1191 ms
2025-08-12 14:56:59,129 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.redisson.Version [L:41] - Redisson 3.10.5
2025-08-12 14:56:59,243 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-7] o.r.c.pool.MasterConnectionPool [L:168] - 1 connections initialized for *********/*********:6379
2025-08-12 14:56:59,243 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-6] o.r.c.p.MasterPubSubConnectionPool [L:168] - 1 connections initialized for *********/*********:6379
2025-08-12 14:56:59,832 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.reflections.Reflections [L:232] - Reflections took 45 ms to scan 5 urls, producing 127 keys and 454 values 
2025-08-12 14:56:59,916 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Stopping service [Tomcat]
2025-08-12 14:56:59,924 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.l.ConditionEvaluationReportLoggingListener [L:142] - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-12 14:59:11,439 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App on chengzhx-MBP with PID 60616 (started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来)
2025-08-12 14:59:11,441 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: test
2025-08-12 14:59:12,128 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-12 14:59:12,130 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-12 14:59:12,166 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 31ms. Found 0 repository interfaces.
2025-08-12 14:59:12,417 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$8a2791c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-12 14:59:12,546 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-12 14:59:12,554 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-12 14:59:12,559 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-12 14:59:12,559 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-12 14:59:12,612 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-12 14:59:12,613 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 1153 ms
2025-08-12 14:59:13,297 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.redisson.Version [L:41] - Redisson 3.10.5
2025-08-12 14:59:13,402 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-7] o.r.c.pool.MasterConnectionPool [L:168] - 1 connections initialized for *********/*********:6379
2025-08-12 14:59:13,402 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-6] o.r.c.p.MasterPubSubConnectionPool [L:168] - 1 connections initialized for *********/*********:6379
2025-08-12 14:59:13,978 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.reflections.Reflections [L:232] - Reflections took 42 ms to scan 5 urls, producing 127 keys and 453 values 
2025-08-12 14:59:14,151 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:83] - 配置数据库认证提供者
2025-08-12 14:59:14,157 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:26] - LoggingAuthenticationProvider 初始化完成
2025-08-12 14:59:14,157 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:27] - UserDetailsService: DatabaseUserDetailsService
2025-08-12 14:59:14,157 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:28] - PasswordEncoder: BCryptPasswordEncoder
2025-08-12 14:59:14,193 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.web.DefaultSecurityFilterChain [L:43] - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@40a28bda, org.springframework.security.web.context.SecurityContextPersistenceFilter@2b99c937, org.springframework.security.web.header.HeaderWriterFilter@1dbff71c, org.springframework.security.web.authentication.logout.LogoutFilter@698ac187, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1de12397, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@563843f1, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@592cb470, org.springframework.security.web.session.SessionManagementFilter@29c25bbc, org.springframework.security.web.access.ExceptionTranslationFilter@7c8f047a, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@1c8f71a7]
2025-08-12 14:59:14,284 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.c.ThreadPoolTaskExecutor [L:171] - Initializing ExecutorService 'applicationTaskExecutor'
2025-08-12 14:59:14,554 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.e.web.EndpointLinksResolver [L:59] - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-08-12 14:59:14,622 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Starting ProtocolHandler ["http-nio-8080"]
2025-08-12 14:59:14,630 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:204] - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-12 14:59:14,632 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:59] - Started App in 3.511 seconds (JVM running for 3.8)
2025-08-12 14:59:15,126 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(3)-*************] com.zaxxer.hikari.HikariDataSource [L:110] - HikariPool-1 - Starting...
2025-08-12 14:59:15,128 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(4)-*************] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-12 14:59:15,128 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(4)-*************] o.s.web.servlet.DispatcherServlet [L:524] - Initializing Servlet 'dispatcherServlet'
2025-08-12 14:59:15,133 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(4)-*************] o.s.web.servlet.DispatcherServlet [L:546] - Completed initialization in 5 ms
2025-08-12 14:59:15,240 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(3)-*************] com.zaxxer.hikari.HikariDataSource [L:123] - HikariPool-1 - Start completed.
2025-08-12 14:59:32,967 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.LoggingAuthenticationProvider [L:33] - === 开始认证流程 ===
2025-08-12 14:59:32,967 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.LoggingAuthenticationProvider [L:38] - 认证请求 - 用户名: admin
2025-08-12 14:59:32,967 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.LoggingAuthenticationProvider [L:39] - 认证请求 - 密码长度: 8
2025-08-12 14:59:32,968 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.LoggingAuthenticationProvider [L:40] - 认证请求 - 密码内容: admin123
2025-08-12 14:59:33,035 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:44] - === 开始数据库用户认证流程 ===
2025-08-12 14:59:33,035 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:45] - 请求认证的用户名: admin
2025-08-12 14:59:33,087 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:54] - ✅ 找到用户: admin (ID: 1)
2025-08-12 14:59:33,087 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:55] - 用户编码: ADMIN001
2025-08-12 14:59:33,087 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:56] - 真实姓名: 系统管理员
2025-08-12 14:59:33,087 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:57] - OA姓名: 系统管理员
2025-08-12 14:59:33,087 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:58] - 用户状态: 1
2025-08-12 14:59:33,088 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:59] - 数据库密码哈希: $2b$12$4NjzpBfiOsziYLFEBgXvAOsgiiye3oEgFolzDumtBGpIdcp2/IvwW
2025-08-12 14:59:33,088 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:60] - 密码哈希长度: 60
2025-08-12 14:59:33,088 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:61] - 密码哈希格式: $2b$12$4Nj
2025-08-12 14:59:33,088 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:64] - === 检查用户状态 ===
2025-08-12 14:59:33,088 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:66] - 用户是否启用: true
2025-08-12 14:59:33,089 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:74] - 账户是否锁定: false
2025-08-12 14:59:33,089 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:75] - 账户锁定时间: null
2025-08-12 14:59:33,089 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:82] - 密码是否过期: false
2025-08-12 14:59:33,089 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:83] - 密码更新时间: 2025-08-12T14:02:18
2025-08-12 14:59:33,089 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:89] - === 加载用户权限 ===
2025-08-12 14:59:33,095 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:94] - 用户权限列表: [ROLE_ADMIN]
2025-08-12 14:59:33,095 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:95] - 权限数量: 1
2025-08-12 14:59:33,096 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:98] - === 构建UserDetails对象 ===
2025-08-12 14:59:33,096 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:99] - 用户名: admin
2025-08-12 14:59:33,096 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:100] - 密码哈希: $2b$12$4NjzpBfiOsziYLFEBgXvAOsgiiye3oEgFolzDumtBGpIdcp2/IvwW
2025-08-12 14:59:33,096 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:101] - 账户过期: false
2025-08-12 14:59:33,096 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:102] - 账户锁定: false
2025-08-12 14:59:33,096 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:103] - 凭据过期: false
2025-08-12 14:59:33,096 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:104] - 账户禁用: false
2025-08-12 14:59:33,097 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:116] - ✅ UserDetails构建完成，返回给Spring Security进行密码验证
2025-08-12 14:59:33,097 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:117] - === DatabaseUserDetailsService处理完成 ===
2025-08-12 14:59:33,098 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.LoggingAuthenticationProvider [L:58] - === 开始密码验证 ===
2025-08-12 14:59:33,098 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.LoggingAuthenticationProvider [L:64] - 用户名: admin
2025-08-12 14:59:33,098 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.LoggingAuthenticationProvider [L:65] - 输入密码: admin123
2025-08-12 14:59:33,098 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.LoggingAuthenticationProvider [L:66] - 存储密码哈希: $2b$12$4NjzpBfiOsziYLFEBgXvAOsgiiye3oEgFolzDumtBGpIdcp2/IvwW
2025-08-12 14:59:33,098 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.LoggingAuthenticationProvider [L:67] - 存储密码哈希长度: 60
2025-08-12 14:59:33,098 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.LoggingAuthenticationProvider [L:68] - 存储密码哈希格式: $2b$12$4Nj
2025-08-12 14:59:33,098 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.LoggingAuthenticationProvider [L:71] - 使用的密码编码器: BCryptPasswordEncoder
2025-08-12 14:59:33,098 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.LoggingAuthenticationProvider [L:84] - 开始执行密码匹配...
2025-08-12 14:59:33,099 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.LoggingAuthenticationProvider [L:86] - 密码匹配结果: false
2025-08-12 14:59:33,100 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.LoggingAuthenticationProvider [L:95] - 尝试手动BCrypt验证...
2025-08-12 14:59:33,100 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.LoggingAuthenticationProvider [L:98] - 检测到BCrypt格式密码
2025-08-12 14:59:33,118 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.LoggingAuthenticationProvider [L:33] - === 开始认证流程 ===
2025-08-12 14:59:33,118 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.LoggingAuthenticationProvider [L:38] - 认证请求 - 用户名: testuser
2025-08-12 14:59:33,118 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.LoggingAuthenticationProvider [L:39] - 认证请求 - 密码长度: 7
2025-08-12 14:59:33,118 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.LoggingAuthenticationProvider [L:40] - 认证请求 - 密码内容: test123
2025-08-12 14:59:33,118 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:44] - === 开始数据库用户认证流程 ===
2025-08-12 14:59:33,119 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:45] - 请求认证的用户名: testuser
2025-08-12 14:59:33,123 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:54] - ✅ 找到用户: testuser (ID: 3)
2025-08-12 14:59:33,124 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:55] - 用户编码: TEST001
2025-08-12 14:59:33,124 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:56] - 真实姓名: 测试用户
2025-08-12 14:59:33,124 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:57] - OA姓名: 测试用户OA
2025-08-12 14:59:33,124 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:58] - 用户状态: 1
2025-08-12 14:59:33,124 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:59] - 数据库密码哈希: $2a$10$lNFWqtbAbNqUXQZDtNcRxeFWX2ZUrG3YQ4reQqeL3ial08S84DqpS
2025-08-12 14:59:33,124 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:60] - 密码哈希长度: 60
2025-08-12 14:59:33,124 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:61] - 密码哈希格式: $2a$10$lNF
2025-08-12 14:59:33,124 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:64] - === 检查用户状态 ===
2025-08-12 14:59:33,124 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:66] - 用户是否启用: true
2025-08-12 14:59:33,124 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:74] - 账户是否锁定: false
2025-08-12 14:59:33,125 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:75] - 账户锁定时间: null
2025-08-12 14:59:33,125 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:82] - 密码是否过期: false
2025-08-12 14:59:33,125 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:83] - 密码更新时间: 2025-08-12T14:37:18
2025-08-12 14:59:33,125 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:89] - === 加载用户权限 ===
2025-08-12 14:59:33,128 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:94] - 用户权限列表: [ROLE_USER]
2025-08-12 14:59:33,128 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:95] - 权限数量: 1
2025-08-12 14:59:33,128 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:98] - === 构建UserDetails对象 ===
2025-08-12 14:59:33,128 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:99] - 用户名: testuser
2025-08-12 14:59:33,128 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:100] - 密码哈希: $2a$10$lNFWqtbAbNqUXQZDtNcRxeFWX2ZUrG3YQ4reQqeL3ial08S84DqpS
2025-08-12 14:59:33,128 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:101] - 账户过期: false
2025-08-12 14:59:33,128 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:102] - 账户锁定: false
2025-08-12 14:59:33,128 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:103] - 凭据过期: false
2025-08-12 14:59:33,129 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:104] - 账户禁用: false
2025-08-12 14:59:33,129 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:116] - ✅ UserDetails构建完成，返回给Spring Security进行密码验证
2025-08-12 14:59:33,129 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:117] - === DatabaseUserDetailsService处理完成 ===
2025-08-12 14:59:33,129 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.LoggingAuthenticationProvider [L:58] - === 开始密码验证 ===
2025-08-12 14:59:33,129 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.LoggingAuthenticationProvider [L:64] - 用户名: testuser
2025-08-12 14:59:33,129 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.LoggingAuthenticationProvider [L:65] - 输入密码: test123
2025-08-12 14:59:33,129 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.LoggingAuthenticationProvider [L:66] - 存储密码哈希: $2a$10$lNFWqtbAbNqUXQZDtNcRxeFWX2ZUrG3YQ4reQqeL3ial08S84DqpS
2025-08-12 14:59:33,129 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.LoggingAuthenticationProvider [L:67] - 存储密码哈希长度: 60
2025-08-12 14:59:33,130 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.LoggingAuthenticationProvider [L:68] - 存储密码哈希格式: $2a$10$lNF
2025-08-12 14:59:33,130 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.LoggingAuthenticationProvider [L:71] - 使用的密码编码器: BCryptPasswordEncoder
2025-08-12 14:59:33,130 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.LoggingAuthenticationProvider [L:84] - 开始执行密码匹配...
2025-08-12 14:59:33,195 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.LoggingAuthenticationProvider [L:86] - 密码匹配结果: true
2025-08-12 14:59:33,195 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.LoggingAuthenticationProvider [L:109] - ✅ 密码验证成功
2025-08-12 14:59:33,196 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.LoggingAuthenticationProvider [L:45] - ✅ 认证成功: testuser
2025-08-12 14:59:33,196 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.LoggingAuthenticationProvider [L:46] - 认证结果权限: [ROLE_USER]
2025-08-12 15:01:21,327 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.LoggingAuthenticationProvider [L:33] - === 开始认证流程 ===
2025-08-12 15:01:21,328 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.LoggingAuthenticationProvider [L:38] - 认证请求 - 用户名: admin
2025-08-12 15:01:21,328 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.LoggingAuthenticationProvider [L:39] - 认证请求 - 密码长度: 8
2025-08-12 15:01:21,328 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.LoggingAuthenticationProvider [L:40] - 认证请求 - 密码内容: admin123
2025-08-12 15:01:21,328 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:44] - === 开始数据库用户认证流程 ===
2025-08-12 15:01:21,328 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:45] - 请求认证的用户名: admin
2025-08-12 15:01:21,335 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:54] - ✅ 找到用户: admin (ID: 1)
2025-08-12 15:01:21,335 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:55] - 用户编码: ADMIN001
2025-08-12 15:01:21,335 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:56] - 真实姓名: 系统管理员
2025-08-12 15:01:21,335 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:57] - OA姓名: 系统管理员
2025-08-12 15:01:21,335 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:58] - 用户状态: 1
2025-08-12 15:01:21,335 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:59] - 数据库密码哈希: $2b$12$4NjzpBfiOsziYLFEBgXvAOsgiiye3oEgFolzDumtBGpIdcp2/IvwW
2025-08-12 15:01:21,335 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:60] - 密码哈希长度: 60
2025-08-12 15:01:21,335 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:61] - 密码哈希格式: $2b$12$4Nj
2025-08-12 15:01:21,335 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:64] - === 检查用户状态 ===
2025-08-12 15:01:21,336 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:66] - 用户是否启用: true
2025-08-12 15:01:21,336 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:74] - 账户是否锁定: false
2025-08-12 15:01:21,336 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:75] - 账户锁定时间: null
2025-08-12 15:01:21,336 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:82] - 密码是否过期: false
2025-08-12 15:01:21,336 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:83] - 密码更新时间: 2025-08-12T14:02:18
2025-08-12 15:01:21,336 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:89] - === 加载用户权限 ===
2025-08-12 15:01:21,340 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:94] - 用户权限列表: [ROLE_ADMIN]
2025-08-12 15:01:21,340 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:95] - 权限数量: 1
2025-08-12 15:01:21,340 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:98] - === 构建UserDetails对象 ===
2025-08-12 15:01:21,340 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:99] - 用户名: admin
2025-08-12 15:01:21,340 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:100] - 密码哈希: $2b$12$4NjzpBfiOsziYLFEBgXvAOsgiiye3oEgFolzDumtBGpIdcp2/IvwW
2025-08-12 15:01:21,341 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:101] - 账户过期: false
2025-08-12 15:01:21,341 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:102] - 账户锁定: false
2025-08-12 15:01:21,341 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:103] - 凭据过期: false
2025-08-12 15:01:21,341 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:104] - 账户禁用: false
2025-08-12 15:01:21,341 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:116] - ✅ UserDetails构建完成，返回给Spring Security进行密码验证
2025-08-12 15:01:21,341 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:117] - === DatabaseUserDetailsService处理完成 ===
2025-08-12 15:01:21,341 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.LoggingAuthenticationProvider [L:58] - === 开始密码验证 ===
2025-08-12 15:01:21,341 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.LoggingAuthenticationProvider [L:64] - 用户名: admin
2025-08-12 15:01:21,341 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.LoggingAuthenticationProvider [L:65] - 输入密码: admin123
2025-08-12 15:01:21,341 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.LoggingAuthenticationProvider [L:66] - 存储密码哈希: $2b$12$4NjzpBfiOsziYLFEBgXvAOsgiiye3oEgFolzDumtBGpIdcp2/IvwW
2025-08-12 15:01:21,341 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.LoggingAuthenticationProvider [L:67] - 存储密码哈希长度: 60
2025-08-12 15:01:21,342 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.LoggingAuthenticationProvider [L:68] - 存储密码哈希格式: $2b$12$4Nj
2025-08-12 15:01:21,342 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.LoggingAuthenticationProvider [L:71] - 使用的密码编码器: BCryptPasswordEncoder
2025-08-12 15:01:21,342 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.LoggingAuthenticationProvider [L:84] - 开始执行密码匹配...
2025-08-12 15:01:21,343 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.LoggingAuthenticationProvider [L:86] - 密码匹配结果: false
2025-08-12 15:01:21,343 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.LoggingAuthenticationProvider [L:95] - 尝试手动BCrypt验证...
2025-08-12 15:01:21,343 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.LoggingAuthenticationProvider [L:98] - 检测到BCrypt格式密码
2025-08-12 15:01:22,566 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.LoggingAuthenticationProvider [L:33] - === 开始认证流程 ===
2025-08-12 15:01:22,567 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.LoggingAuthenticationProvider [L:38] - 认证请求 - 用户名: admin
2025-08-12 15:01:22,567 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.LoggingAuthenticationProvider [L:39] - 认证请求 - 密码长度: 8
2025-08-12 15:01:22,567 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.LoggingAuthenticationProvider [L:40] - 认证请求 - 密码内容: admin123
2025-08-12 15:01:22,568 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.DatabaseUserDetailsService [L:44] - === 开始数据库用户认证流程 ===
2025-08-12 15:01:22,568 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.DatabaseUserDetailsService [L:45] - 请求认证的用户名: admin
2025-08-12 15:01:22,575 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.DatabaseUserDetailsService [L:54] - ✅ 找到用户: admin (ID: 1)
2025-08-12 15:01:22,575 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.DatabaseUserDetailsService [L:55] - 用户编码: ADMIN001
2025-08-12 15:01:22,575 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.DatabaseUserDetailsService [L:56] - 真实姓名: 系统管理员
2025-08-12 15:01:22,576 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.DatabaseUserDetailsService [L:57] - OA姓名: 系统管理员
2025-08-12 15:01:22,576 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.DatabaseUserDetailsService [L:58] - 用户状态: 1
2025-08-12 15:01:22,576 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.DatabaseUserDetailsService [L:59] - 数据库密码哈希: $2b$12$4NjzpBfiOsziYLFEBgXvAOsgiiye3oEgFolzDumtBGpIdcp2/IvwW
2025-08-12 15:01:22,576 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.DatabaseUserDetailsService [L:60] - 密码哈希长度: 60
2025-08-12 15:01:22,576 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.DatabaseUserDetailsService [L:61] - 密码哈希格式: $2b$12$4Nj
2025-08-12 15:01:22,576 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.DatabaseUserDetailsService [L:64] - === 检查用户状态 ===
2025-08-12 15:01:22,576 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.DatabaseUserDetailsService [L:66] - 用户是否启用: true
2025-08-12 15:01:22,576 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.DatabaseUserDetailsService [L:74] - 账户是否锁定: false
2025-08-12 15:01:22,576 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.DatabaseUserDetailsService [L:75] - 账户锁定时间: null
2025-08-12 15:01:22,577 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.DatabaseUserDetailsService [L:82] - 密码是否过期: false
2025-08-12 15:01:22,577 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.DatabaseUserDetailsService [L:83] - 密码更新时间: 2025-08-12T14:02:18
2025-08-12 15:01:22,577 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.DatabaseUserDetailsService [L:89] - === 加载用户权限 ===
2025-08-12 15:01:22,580 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.DatabaseUserDetailsService [L:94] - 用户权限列表: [ROLE_ADMIN]
2025-08-12 15:01:22,580 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.DatabaseUserDetailsService [L:95] - 权限数量: 1
2025-08-12 15:01:22,580 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.DatabaseUserDetailsService [L:98] - === 构建UserDetails对象 ===
2025-08-12 15:01:22,580 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.DatabaseUserDetailsService [L:99] - 用户名: admin
2025-08-12 15:01:22,580 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.DatabaseUserDetailsService [L:100] - 密码哈希: $2b$12$4NjzpBfiOsziYLFEBgXvAOsgiiye3oEgFolzDumtBGpIdcp2/IvwW
2025-08-12 15:01:22,581 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.DatabaseUserDetailsService [L:101] - 账户过期: false
2025-08-12 15:01:22,581 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.DatabaseUserDetailsService [L:102] - 账户锁定: false
2025-08-12 15:01:22,581 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.DatabaseUserDetailsService [L:103] - 凭据过期: false
2025-08-12 15:01:22,581 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.DatabaseUserDetailsService [L:104] - 账户禁用: false
2025-08-12 15:01:22,581 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.DatabaseUserDetailsService [L:116] - ✅ UserDetails构建完成，返回给Spring Security进行密码验证
2025-08-12 15:01:22,581 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.DatabaseUserDetailsService [L:117] - === DatabaseUserDetailsService处理完成 ===
2025-08-12 15:01:22,581 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.LoggingAuthenticationProvider [L:58] - === 开始密码验证 ===
2025-08-12 15:01:22,581 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.LoggingAuthenticationProvider [L:64] - 用户名: admin
2025-08-12 15:01:22,581 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.LoggingAuthenticationProvider [L:65] - 输入密码: admin123
2025-08-12 15:01:22,581 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.LoggingAuthenticationProvider [L:66] - 存储密码哈希: $2b$12$4NjzpBfiOsziYLFEBgXvAOsgiiye3oEgFolzDumtBGpIdcp2/IvwW
2025-08-12 15:01:22,581 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.LoggingAuthenticationProvider [L:67] - 存储密码哈希长度: 60
2025-08-12 15:01:22,582 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.LoggingAuthenticationProvider [L:68] - 存储密码哈希格式: $2b$12$4Nj
2025-08-12 15:01:22,582 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.LoggingAuthenticationProvider [L:71] - 使用的密码编码器: BCryptPasswordEncoder
2025-08-12 15:01:22,582 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.LoggingAuthenticationProvider [L:84] - 开始执行密码匹配...
2025-08-12 15:01:22,582 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.LoggingAuthenticationProvider [L:86] - 密码匹配结果: false
2025-08-12 15:01:22,582 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.LoggingAuthenticationProvider [L:95] - 尝试手动BCrypt验证...
2025-08-12 15:01:22,582 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.LoggingAuthenticationProvider [L:98] - 检测到BCrypt格式密码
2025-08-12 15:01:30,126 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-9] o.s.s.c.ThreadPoolTaskExecutor [L:208] - Shutting down ExecutorService 'applicationTaskExecutor'
2025-08-12 15:01:30,147 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-9] com.zaxxer.hikari.HikariDataSource [L:350] - HikariPool-1 - Shutdown initiated...
2025-08-12 15:01:30,150 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-9] com.zaxxer.hikari.HikariDataSource [L:352] - HikariPool-1 - Shutdown completed.
2025-08-12 15:04:27,347 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App on chengzhx-MBP with PID 60919 (started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来)
2025-08-12 15:04:27,349 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: test
2025-08-12 15:04:28,091 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-12 15:04:28,092 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-12 15:04:28,133 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 33ms. Found 0 repository interfaces.
2025-08-12 15:04:28,393 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$740a37bd] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-12 15:04:28,528 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-12 15:04:28,536 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-12 15:04:28,542 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-12 15:04:28,542 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-12 15:04:28,601 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-12 15:04:28,601 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 1232 ms
2025-08-12 15:04:29,320 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.redisson.Version [L:41] - Redisson 3.10.5
2025-08-12 15:04:29,444 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-6] o.r.c.p.MasterPubSubConnectionPool [L:168] - 1 connections initialized for *********/*********:6379
2025-08-12 15:04:29,444 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-7] o.r.c.pool.MasterConnectionPool [L:168] - 1 connections initialized for *********/*********:6379
2025-08-12 15:04:30,029 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.reflections.Reflections [L:232] - Reflections took 43 ms to scan 5 urls, producing 127 keys and 453 values 
2025-08-12 15:04:30,201 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:83] - 配置数据库认证提供者
2025-08-12 15:04:30,207 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:26] - LoggingAuthenticationProvider 初始化完成
2025-08-12 15:04:30,207 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:27] - UserDetailsService: DatabaseUserDetailsService
2025-08-12 15:04:30,207 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:28] - PasswordEncoder: BCryptPasswordEncoder
2025-08-12 15:04:30,243 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.web.DefaultSecurityFilterChain [L:43] - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4ab455e2, org.springframework.security.web.context.SecurityContextPersistenceFilter@6c519e47, org.springframework.security.web.header.HeaderWriterFilter@2c2aab92, org.springframework.security.web.authentication.logout.LogoutFilter@7c8f047a, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4768b95c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1da53f4f, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2acca224, org.springframework.security.web.session.SessionManagementFilter@29aa4bc9, org.springframework.security.web.access.ExceptionTranslationFilter@6cee903a, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@36871e98]
2025-08-12 15:04:30,332 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.c.ThreadPoolTaskExecutor [L:171] - Initializing ExecutorService 'applicationTaskExecutor'
2025-08-12 15:04:30,602 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.e.web.EndpointLinksResolver [L:59] - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-08-12 15:04:30,673 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Starting ProtocolHandler ["http-nio-8080"]
2025-08-12 15:04:30,680 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:204] - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-12 15:04:30,682 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:59] - Started App in 3.671 seconds (JVM running for 3.968)
2025-08-12 15:04:30,935 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(4)-*************] com.zaxxer.hikari.HikariDataSource [L:110] - HikariPool-1 - Starting...
2025-08-12 15:04:30,937 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(3)-*************] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-12 15:04:30,937 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(3)-*************] o.s.web.servlet.DispatcherServlet [L:524] - Initializing Servlet 'dispatcherServlet'
2025-08-12 15:04:30,942 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(3)-*************] o.s.web.servlet.DispatcherServlet [L:546] - Completed initialization in 5 ms
2025-08-12 15:04:31,053 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(4)-*************] com.zaxxer.hikari.HikariDataSource [L:123] - HikariPool-1 - Start completed.
2025-08-12 15:04:33,569 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.LoggingAuthenticationProvider [L:33] - === 开始认证流程 ===
2025-08-12 15:04:33,570 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.LoggingAuthenticationProvider [L:38] - 认证请求 - 用户名: admin
2025-08-12 15:04:33,570 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.LoggingAuthenticationProvider [L:39] - 认证请求 - 密码长度: 8
2025-08-12 15:04:33,570 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.LoggingAuthenticationProvider [L:40] - 认证请求 - 密码内容: admin123
2025-08-12 15:04:33,637 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:45] - === 开始数据库用户认证流程 ===
2025-08-12 15:04:33,637 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:46] - 请求认证的用户名: admin
2025-08-12 15:04:33,690 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:55] - ✅ 找到用户: admin (ID: 1)
2025-08-12 15:04:33,690 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:56] - 用户编码: ADMIN001
2025-08-12 15:04:33,690 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:57] - 真实姓名: 系统管理员
2025-08-12 15:04:33,690 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:58] - OA姓名: 系统管理员
2025-08-12 15:04:33,690 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:59] - 用户状态: 1
2025-08-12 15:04:33,691 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:60] - 数据库密码哈希: $2a$10$FxZeSFOP6xUwUSL5YvJuZ.rh6hLN.rKWP0qMhsDT2votCEdhvG6Yq
2025-08-12 15:04:33,691 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:61] - 密码哈希长度: 60
2025-08-12 15:04:33,691 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:62] - 密码哈希格式: $2a$10$FxZ
2025-08-12 15:04:33,691 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:65] - === 检查用户状态 ===
2025-08-12 15:04:33,691 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:67] - 用户是否启用: true
2025-08-12 15:04:33,691 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:75] - 账户是否锁定: false
2025-08-12 15:04:33,692 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:76] - 账户锁定时间: null
2025-08-12 15:04:33,692 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:83] - 密码是否过期: false
2025-08-12 15:04:33,692 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:84] - 密码更新时间: 2025-08-12T14:02:18
2025-08-12 15:04:33,692 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:90] - === 加载用户权限 ===
2025-08-12 15:04:33,698 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:95] - 用户权限列表: [ROLE_ADMIN]
2025-08-12 15:04:33,698 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:96] - 权限数量: 1
2025-08-12 15:04:33,699 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:99] - === 构建UserDetails对象 ===
2025-08-12 15:04:33,699 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:100] - 用户名: admin
2025-08-12 15:04:33,699 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:101] - 密码哈希: $2a$10$FxZeSFOP6xUwUSL5YvJuZ.rh6hLN.rKWP0qMhsDT2votCEdhvG6Yq
2025-08-12 15:04:33,699 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:102] - 账户过期: false
2025-08-12 15:04:33,699 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:103] - 账户锁定: false
2025-08-12 15:04:33,699 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:104] - 凭据过期: false
2025-08-12 15:04:33,699 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:105] - 账户禁用: false
2025-08-12 15:04:33,700 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:117] - ✅ UserDetails构建完成，返回给Spring Security进行密码验证
2025-08-12 15:04:33,700 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:118] - === DatabaseUserDetailsService处理完成 ===
2025-08-12 15:04:33,700 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.LoggingAuthenticationProvider [L:58] - === 开始密码验证 ===
2025-08-12 15:04:33,701 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.LoggingAuthenticationProvider [L:64] - 用户名: admin
2025-08-12 15:04:33,701 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.LoggingAuthenticationProvider [L:65] - 输入密码: admin123
2025-08-12 15:04:33,701 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.LoggingAuthenticationProvider [L:66] - 存储密码哈希: $2a$10$FxZeSFOP6xUwUSL5YvJuZ.rh6hLN.rKWP0qMhsDT2votCEdhvG6Yq
2025-08-12 15:04:33,701 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.LoggingAuthenticationProvider [L:67] - 存储密码哈希长度: 60
2025-08-12 15:04:33,701 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.LoggingAuthenticationProvider [L:68] - 存储密码哈希格式: $2a$10$FxZ
2025-08-12 15:04:33,701 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.LoggingAuthenticationProvider [L:71] - 使用的密码编码器: BCryptPasswordEncoder
2025-08-12 15:04:33,701 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.LoggingAuthenticationProvider [L:84] - 开始执行密码匹配...
2025-08-12 15:04:33,766 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.LoggingAuthenticationProvider [L:86] - 密码匹配结果: true
2025-08-12 15:04:33,766 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.LoggingAuthenticationProvider [L:109] - ✅ 密码验证成功
2025-08-12 15:04:33,766 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.LoggingAuthenticationProvider [L:45] - ✅ 认证成功: admin
2025-08-12 15:04:33,767 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.LoggingAuthenticationProvider [L:46] - 认证结果权限: [ROLE_ADMIN]
2025-08-12 15:05:36,249 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-9] o.s.s.c.ThreadPoolTaskExecutor [L:208] - Shutting down ExecutorService 'applicationTaskExecutor'
2025-08-12 15:05:36,269 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-9] com.zaxxer.hikari.HikariDataSource [L:350] - HikariPool-1 - Shutdown initiated...
2025-08-12 15:05:36,271 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-9] com.zaxxer.hikari.HikariDataSource [L:352] - HikariPool-1 - Shutdown completed.
2025-08-12 15:07:47,474 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App on chengzhx-MBP with PID 61080 (started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来)
2025-08-12 15:07:47,477 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: test
2025-08-12 15:07:48,121 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-12 15:07:48,122 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-12 15:07:48,159 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 32ms. Found 0 repository interfaces.
2025-08-12 15:07:48,410 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$bd7d0939] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-12 15:07:48,534 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-12 15:07:48,542 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-12 15:07:48,547 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-12 15:07:48,547 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-12 15:07:48,600 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-12 15:07:48,600 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 1105 ms
2025-08-12 15:07:49,309 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.redisson.Version [L:41] - Redisson 3.10.5
2025-08-12 15:07:49,425 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-7] o.r.c.p.MasterPubSubConnectionPool [L:168] - 1 connections initialized for *********/*********:6379
2025-08-12 15:07:49,425 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-6] o.r.c.pool.MasterConnectionPool [L:168] - 1 connections initialized for *********/*********:6379
2025-08-12 15:07:50,015 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.reflections.Reflections [L:232] - Reflections took 45 ms to scan 5 urls, producing 127 keys and 453 values 
2025-08-12 15:07:50,180 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:83] - 配置数据库认证提供者
2025-08-12 15:07:50,186 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:26] - LoggingAuthenticationProvider 初始化完成
2025-08-12 15:07:50,187 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:27] - UserDetailsService: DatabaseUserDetailsService
2025-08-12 15:07:50,187 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:28] - PasswordEncoder: BCryptPasswordEncoder
2025-08-12 15:07:50,221 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.web.DefaultSecurityFilterChain [L:43] - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@72a34537, org.springframework.security.web.context.SecurityContextPersistenceFilter@1c7843c3, org.springframework.security.web.header.HeaderWriterFilter@1da53f4f, org.springframework.security.web.authentication.logout.LogoutFilter@27dfd12b, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7c8f047a, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6abe62bb, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@24fef542, org.springframework.security.web.session.SessionManagementFilter@1d585fb, org.springframework.security.web.access.ExceptionTranslationFilter@29f95272, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@92e2c93]
2025-08-12 15:07:50,305 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.c.ThreadPoolTaskExecutor [L:171] - Initializing ExecutorService 'applicationTaskExecutor'
2025-08-12 15:07:50,574 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.e.web.EndpointLinksResolver [L:59] - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-08-12 15:07:50,640 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Starting ProtocolHandler ["http-nio-8080"]
2025-08-12 15:07:50,646 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:204] - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-12 15:07:50,648 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:59] - Started App in 3.492 seconds (JVM running for 3.786)
2025-08-12 15:07:51,087 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(3)-*************] com.zaxxer.hikari.HikariDataSource [L:110] - HikariPool-1 - Starting...
2025-08-12 15:07:51,088 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(1)-*************] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-12 15:07:51,089 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(1)-*************] o.s.web.servlet.DispatcherServlet [L:524] - Initializing Servlet 'dispatcherServlet'
2025-08-12 15:07:51,094 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(1)-*************] o.s.web.servlet.DispatcherServlet [L:546] - Completed initialization in 5 ms
2025-08-12 15:07:51,187 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(3)-*************] com.zaxxer.hikari.HikariDataSource [L:123] - HikariPool-1 - Start completed.
2025-08-12 15:07:54,983 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-9] o.s.s.c.ThreadPoolTaskExecutor [L:208] - Shutting down ExecutorService 'applicationTaskExecutor'
2025-08-12 15:07:55,010 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-9] com.zaxxer.hikari.HikariDataSource [L:350] - HikariPool-1 - Shutdown initiated...
2025-08-12 15:07:55,013 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-9] com.zaxxer.hikari.HikariDataSource [L:352] - HikariPool-1 - Shutdown completed.
2025-08-12 15:12:40,597 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App on chengzhx-MBP with PID 61347 (started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来)
2025-08-12 15:12:40,598 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: dev
2025-08-12 15:12:41,230 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-12 15:12:41,231 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-12 15:12:41,270 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 32ms. Found 0 repository interfaces.
2025-08-12 15:12:41,513 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$27e1ce21] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-12 15:12:41,639 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-12 15:12:41,647 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-12 15:12:41,653 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-12 15:12:41,653 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-12 15:12:41,707 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-12 15:12:41,707 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 1088 ms
2025-08-12 15:24:49,584 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App on chengzhx-MBP with PID 62617 (started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来)
2025-08-12 15:24:49,586 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: test
2025-08-12 15:24:50,229 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-12 15:24:50,230 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-12 15:24:50,268 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 31ms. Found 0 repository interfaces.
2025-08-12 15:24:50,515 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$b44ed174] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-12 15:24:50,643 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-12 15:24:50,651 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-12 15:24:50,657 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-12 15:24:50,657 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-12 15:24:50,712 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-12 15:24:50,712 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 1107 ms
2025-08-12 15:24:51,417 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.redisson.Version [L:41] - Redisson 3.10.5
2025-08-12 15:24:51,525 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-6] o.r.c.pool.MasterConnectionPool [L:168] - 1 connections initialized for *********/*********:6379
2025-08-12 15:24:51,525 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-7] o.r.c.p.MasterPubSubConnectionPool [L:168] - 1 connections initialized for *********/*********:6379
2025-08-12 15:24:52,114 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.reflections.Reflections [L:232] - Reflections took 43 ms to scan 5 urls, producing 127 keys and 453 values 
2025-08-12 15:24:52,279 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:83] - 配置数据库认证提供者
2025-08-12 15:24:52,285 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:26] - LoggingAuthenticationProvider 初始化完成
2025-08-12 15:24:52,286 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:27] - UserDetailsService: DatabaseUserDetailsService
2025-08-12 15:24:52,286 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:28] - PasswordEncoder: BCryptPasswordEncoder
2025-08-12 15:24:52,321 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.web.DefaultSecurityFilterChain [L:43] - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@65f5cae3, org.springframework.security.web.context.SecurityContextPersistenceFilter@340c57e0, org.springframework.security.web.header.HeaderWriterFilter@7d126083, org.springframework.security.web.authentication.logout.LogoutFilter@3ce7490a, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7c9372ed, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@780ee3ad, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6630dd28, org.springframework.security.web.session.SessionManagementFilter@20a9f5fb, org.springframework.security.web.access.ExceptionTranslationFilter@7ee64b53, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@56872fcb]
2025-08-12 15:24:52,406 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.c.ThreadPoolTaskExecutor [L:171] - Initializing ExecutorService 'applicationTaskExecutor'
2025-08-12 15:24:52,668 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.e.web.EndpointLinksResolver [L:59] - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-08-12 15:24:52,733 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Starting ProtocolHandler ["http-nio-8080"]
2025-08-12 15:24:52,739 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:204] - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-12 15:24:52,741 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:59] - Started App in 3.468 seconds (JVM running for 3.757)
2025-08-12 15:24:53,221 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(2)-*************] com.zaxxer.hikari.HikariDataSource [L:110] - HikariPool-1 - Starting...
2025-08-12 15:24:53,223 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(1)-*************] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-12 15:24:53,223 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(1)-*************] o.s.web.servlet.DispatcherServlet [L:524] - Initializing Servlet 'dispatcherServlet'
2025-08-12 15:24:53,228 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(1)-*************] o.s.web.servlet.DispatcherServlet [L:546] - Completed initialization in 5 ms
2025-08-12 15:24:53,335 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(2)-*************] com.zaxxer.hikari.HikariDataSource [L:123] - HikariPool-1 - Start completed.
2025-08-12 15:24:58,924 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-9] o.s.s.c.ThreadPoolTaskExecutor [L:208] - Shutting down ExecutorService 'applicationTaskExecutor'
2025-08-12 15:24:58,951 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-9] com.zaxxer.hikari.HikariDataSource [L:350] - HikariPool-1 - Shutdown initiated...
2025-08-12 15:24:58,955 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-9] com.zaxxer.hikari.HikariDataSource [L:352] - HikariPool-1 - Shutdown completed.
2025-08-12 15:25:56,963 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App on chengzhx-MBP with PID 62672 (started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来)
2025-08-12 15:25:56,964 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: test
2025-08-12 15:25:57,607 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-12 15:25:57,608 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-12 15:25:57,645 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 32ms. Found 0 repository interfaces.
2025-08-12 15:25:57,893 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$b66ee729] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-12 15:25:58,017 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-12 15:25:58,024 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-12 15:25:58,029 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-12 15:25:58,030 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-12 15:25:58,081 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-12 15:25:58,082 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 1099 ms
2025-08-12 15:25:58,790 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.redisson.Version [L:41] - Redisson 3.10.5
2025-08-12 15:25:58,904 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-6] o.r.c.pool.MasterConnectionPool [L:168] - 1 connections initialized for *********/*********:6379
2025-08-12 15:25:58,904 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-7] o.r.c.p.MasterPubSubConnectionPool [L:168] - 1 connections initialized for *********/*********:6379
2025-08-12 15:25:59,471 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.reflections.Reflections [L:232] - Reflections took 45 ms to scan 5 urls, producing 127 keys and 453 values 
2025-08-12 15:25:59,649 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:83] - 配置数据库认证提供者
2025-08-12 15:25:59,655 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:26] - LoggingAuthenticationProvider 初始化完成
2025-08-12 15:25:59,655 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:27] - UserDetailsService: DatabaseUserDetailsService
2025-08-12 15:25:59,655 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:28] - PasswordEncoder: BCryptPasswordEncoder
2025-08-12 15:25:59,691 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.web.DefaultSecurityFilterChain [L:43] - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@61c4cebd, org.springframework.security.web.context.SecurityContextPersistenceFilter@7ee64b53, org.springframework.security.web.header.HeaderWriterFilter@7462ba4b, org.springframework.security.web.authentication.logout.LogoutFilter@780ee3ad, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7d126083, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@760a2b6e, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@655909e2, org.springframework.security.web.session.SessionManagementFilter@76134b9b, org.springframework.security.web.access.ExceptionTranslationFilter@54e0f76f, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2f5f5bc9]
2025-08-12 15:25:59,776 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.c.ThreadPoolTaskExecutor [L:171] - Initializing ExecutorService 'applicationTaskExecutor'
2025-08-12 15:26:00,044 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.e.web.EndpointLinksResolver [L:59] - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-08-12 15:26:00,111 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Starting ProtocolHandler ["http-nio-8080"]
2025-08-12 15:26:00,117 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:204] - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-12 15:26:00,119 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:59] - Started App in 3.475 seconds (JVM running for 3.811)
2025-08-12 15:26:00,451 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(1)-*************] com.zaxxer.hikari.HikariDataSource [L:110] - HikariPool-1 - Starting...
2025-08-12 15:26:00,452 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(3)-*************] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-12 15:26:00,453 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(3)-*************] o.s.web.servlet.DispatcherServlet [L:524] - Initializing Servlet 'dispatcherServlet'
2025-08-12 15:26:00,458 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(3)-*************] o.s.web.servlet.DispatcherServlet [L:546] - Completed initialization in 5 ms
2025-08-12 15:26:00,555 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(1)-*************] com.zaxxer.hikari.HikariDataSource [L:123] - HikariPool-1 - Start completed.
2025-08-12 15:26:05,600 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-10] o.s.s.c.ThreadPoolTaskExecutor [L:208] - Shutting down ExecutorService 'applicationTaskExecutor'
2025-08-12 15:26:05,618 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-10] com.zaxxer.hikari.HikariDataSource [L:350] - HikariPool-1 - Shutdown initiated...
2025-08-12 15:26:05,621 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-10] com.zaxxer.hikari.HikariDataSource [L:352] - HikariPool-1 - Shutdown completed.
2025-08-12 15:36:52,053 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App on chengzhx-MBP with PID 63211 (started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来)
2025-08-12 15:36:52,055 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: test
2025-08-12 15:36:52,727 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-12 15:36:52,728 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-12 15:36:52,764 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 31ms. Found 0 repository interfaces.
2025-08-12 15:36:53,013 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$8e3fa244] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-12 15:36:53,130 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-12 15:36:53,138 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-12 15:36:53,143 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-12 15:36:53,143 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-12 15:36:53,195 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-12 15:36:53,195 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 1120 ms
2025-08-12 15:36:53,905 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.redisson.Version [L:41] - Redisson 3.10.5
2025-08-12 15:36:54,020 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-7] o.r.c.pool.MasterConnectionPool [L:168] - 1 connections initialized for *********/*********:6379
2025-08-12 15:36:54,020 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-6] o.r.c.p.MasterPubSubConnectionPool [L:168] - 1 connections initialized for *********/*********:6379
2025-08-12 15:38:52,012 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App on chengzhx-MBP with PID 63354 (started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来)
2025-08-12 15:38:52,014 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: test
2025-08-12 15:38:52,663 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-12 15:38:52,664 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-12 15:38:52,701 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 31ms. Found 0 repository interfaces.
2025-08-12 15:38:52,953 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$f370ae15] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-12 15:38:53,070 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-12 15:38:53,078 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-12 15:38:53,083 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-12 15:38:53,083 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-12 15:38:53,134 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-12 15:38:53,135 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 1103 ms
2025-08-12 15:38:53,832 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.redisson.Version [L:41] - Redisson 3.10.5
2025-08-12 15:38:53,935 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-7] o.r.c.p.MasterPubSubConnectionPool [L:168] - 1 connections initialized for *********/*********:6379
2025-08-12 15:38:53,935 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-6] o.r.c.pool.MasterConnectionPool [L:168] - 1 connections initialized for *********/*********:6379
2025-08-12 15:38:54,491 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.reflections.Reflections [L:232] - Reflections took 44 ms to scan 5 urls, producing 127 keys and 453 values 
2025-08-12 15:38:54,657 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:83] - 配置数据库认证提供者
2025-08-12 15:38:54,663 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:26] - LoggingAuthenticationProvider 初始化完成
2025-08-12 15:38:54,663 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:27] - UserDetailsService: DatabaseUserDetailsService
2025-08-12 15:38:54,663 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:28] - PasswordEncoder: BCryptPasswordEncoder
2025-08-12 15:38:54,696 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.web.DefaultSecurityFilterChain [L:43] - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@25d0b918, org.springframework.security.web.context.SecurityContextPersistenceFilter@284c4f02, org.springframework.security.web.header.HeaderWriterFilter@39a8e2fa, org.springframework.security.web.authentication.logout.LogoutFilter@60ae7cf2, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1a43a88e, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@75a226ea, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@9bf63d2, org.springframework.security.web.session.SessionManagementFilter@2c08c787, org.springframework.security.web.access.ExceptionTranslationFilter@2f038d3c, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@302da330]
2025-08-12 15:38:54,783 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.c.ThreadPoolTaskExecutor [L:171] - Initializing ExecutorService 'applicationTaskExecutor'
2025-08-12 15:38:55,044 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.e.web.EndpointLinksResolver [L:59] - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-08-12 15:38:55,110 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Starting ProtocolHandler ["http-nio-8080"]
2025-08-12 15:38:55,116 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:204] - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-12 15:38:55,118 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:59] - Started App in 3.428 seconds (JVM running for 3.726)
2025-08-12 15:38:55,613 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(1)-*************] com.zaxxer.hikari.HikariDataSource [L:110] - HikariPool-1 - Starting...
2025-08-12 15:38:55,615 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(3)-*************] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-12 15:38:55,615 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(3)-*************] o.s.web.servlet.DispatcherServlet [L:524] - Initializing Servlet 'dispatcherServlet'
2025-08-12 15:38:55,620 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(3)-*************] o.s.web.servlet.DispatcherServlet [L:546] - Completed initialization in 5 ms
2025-08-12 15:38:55,717 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(1)-*************] com.zaxxer.hikari.HikariDataSource [L:123] - HikariPool-1 - Start completed.
2025-08-12 15:38:58,114 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-9] o.s.s.c.ThreadPoolTaskExecutor [L:208] - Shutting down ExecutorService 'applicationTaskExecutor'
2025-08-12 15:38:58,153 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-9] com.zaxxer.hikari.HikariDataSource [L:350] - HikariPool-1 - Shutdown initiated...
2025-08-12 15:38:58,157 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-9] com.zaxxer.hikari.HikariDataSource [L:352] - HikariPool-1 - Shutdown completed.
2025-08-12 15:39:14,829 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App on chengzhx-MBP with PID 63376 (started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来)
2025-08-12 15:39:14,847 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: test
2025-08-12 15:39:15,520 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-12 15:39:15,521 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-12 15:39:15,558 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 32ms. Found 0 repository interfaces.
2025-08-12 15:39:15,797 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$161d1f3c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-12 15:39:15,917 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-12 15:39:15,925 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-12 15:39:15,930 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-12 15:39:15,930 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-12 15:39:15,981 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-12 15:39:15,981 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 1114 ms
2025-08-12 15:39:16,677 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.redisson.Version [L:41] - Redisson 3.10.5
2025-08-12 15:39:16,787 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-6] o.r.c.pool.MasterConnectionPool [L:168] - 1 connections initialized for *********/*********:63791
2025-08-12 15:39:16,787 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-7] o.r.c.p.MasterPubSubConnectionPool [L:168] - 1 connections initialized for *********/*********:63791
2025-08-12 15:39:17,384 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.reflections.Reflections [L:232] - Reflections took 43 ms to scan 5 urls, producing 127 keys and 453 values 
2025-08-12 15:39:17,546 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:86] - 配置固定用户认证提供者
2025-08-12 15:39:17,552 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:26] - LoggingAuthenticationProvider 初始化完成
2025-08-12 15:39:17,552 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:27] - UserDetailsService: FixedUserDetailsService
2025-08-12 15:39:17,552 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:28] - PasswordEncoder: BCryptPasswordEncoder
2025-08-12 15:39:17,588 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.web.DefaultSecurityFilterChain [L:43] - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@10a0d93a, org.springframework.security.web.context.SecurityContextPersistenceFilter@3b33fff9, org.springframework.security.web.header.HeaderWriterFilter@136fece2, org.springframework.security.web.authentication.logout.LogoutFilter@63c84d31, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@fee881, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4ca0b9b1, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1f3c5308, org.springframework.security.web.session.SessionManagementFilter@7bce9ce4, org.springframework.security.web.access.ExceptionTranslationFilter@334b392d, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@4ca970d5]
2025-08-12 15:39:17,674 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.c.ThreadPoolTaskExecutor [L:171] - Initializing ExecutorService 'applicationTaskExecutor'
2025-08-12 15:39:17,938 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.e.web.EndpointLinksResolver [L:59] - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-08-12 15:39:18,000 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Starting ProtocolHandler ["http-nio-8080"]
2025-08-12 15:39:18,006 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:204] - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-12 15:39:18,008 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:59] - Started App in 3.489 seconds (JVM running for 3.778)
2025-08-12 15:39:18,449 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(4)-*************] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-12 15:39:18,449 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(4)-*************] o.s.web.servlet.DispatcherServlet [L:524] - Initializing Servlet 'dispatcherServlet'
2025-08-12 15:39:18,457 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(4)-*************] o.s.web.servlet.DispatcherServlet [L:546] - Completed initialization in 8 ms
2025-08-12 15:39:18,458 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(3)-*************] com.zaxxer.hikari.HikariDataSource [L:110] - HikariPool-1 - Starting...
2025-08-12 15:39:18,559 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(3)-*************] com.zaxxer.hikari.HikariDataSource [L:123] - HikariPool-1 - Start completed.
2025-08-12 15:39:19,486 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-9] o.s.s.c.ThreadPoolTaskExecutor [L:208] - Shutting down ExecutorService 'applicationTaskExecutor'
2025-08-12 15:39:19,505 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-9] com.zaxxer.hikari.HikariDataSource [L:350] - HikariPool-1 - Shutdown initiated...
2025-08-12 15:39:19,508 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-9] com.zaxxer.hikari.HikariDataSource [L:352] - HikariPool-1 - Shutdown completed.
