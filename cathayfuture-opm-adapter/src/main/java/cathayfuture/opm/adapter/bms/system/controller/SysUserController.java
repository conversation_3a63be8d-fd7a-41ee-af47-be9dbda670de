package cathayfuture.opm.adapter.bms.system.controller;

import cathayfuture.opm.app.system.service.SysUserAppService;
import cathayfuture.opm.client.system.dto.SysUserDTO;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 系统用户管理控制器
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@Slf4j
@RestController
@RequestMapping("/bms/system/user")
@Tag(name = "系统用户管理", description = "系统用户增删改查、角色分配等功能")
@Validated
public class SysUserController {

    private final SysUserAppService userAppService;

    public SysUserController(SysUserAppService userAppService) {
        this.userAppService = userAppService;
    }

    @PostMapping
    @Operation(summary = "创建用户", description = "创建新的系统用户")
    @PreAuthorize("hasAuthority('ROLE_ADMIN') or hasAuthority('system:user:add')")
    public ResponseEntity<SysUserDTO> createUser(@Valid @RequestBody SysUserDTO.CreateRequest request) {
        try {
            SysUserDTO result = userAppService.createUser(request);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("创建用户失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    @PutMapping("/{id}")
    @Operation(summary = "更新用户", description = "更新用户信息")
    @PreAuthorize("hasAuthority('ROLE_ADMIN') or hasAuthority('system:user:edit')")
    public ResponseEntity<SysUserDTO> updateUser(
            @Parameter(description = "用户ID") @PathVariable Integer id,
            @Valid @RequestBody SysUserDTO.UpdateRequest request) {
        try {
            request.setId(id);
            SysUserDTO result = userAppService.updateUser(request);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("更新用户失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除用户", description = "删除指定用户")
    @PreAuthorize("hasAuthority('ROLE_ADMIN') or hasAuthority('system:user:delete')")
    public ResponseEntity<Void> deleteUser(@Parameter(description = "用户ID") @PathVariable Integer id) {
        try {
            boolean result = userAppService.deleteUser(id);
            return result ? ResponseEntity.ok().build() : ResponseEntity.badRequest().build();
        } catch (Exception e) {
            log.error("删除用户失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    @GetMapping("/{id}")
    @Operation(summary = "查询用户", description = "根据ID查询用户详情")
    @PreAuthorize("hasAuthority('ROLE_ADMIN') or hasAuthority('system:user:list')")
    public ResponseEntity<SysUserDTO> getUserById(@Parameter(description = "用户ID") @PathVariable Integer id) {
        try {
            SysUserDTO result = userAppService.getUserById(id);
            return result != null ? ResponseEntity.ok(result) : ResponseEntity.notFound().build();
        } catch (Exception e) {
            log.error("查询用户失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    @GetMapping("/list")
    @Operation(summary = "查询用户列表", description = "根据条件查询用户列表")
    @PreAuthorize("hasAuthority('ROLE_ADMIN') or hasAuthority('system:user:list')")
    public ResponseEntity<List<SysUserDTO>> getUserList(@Valid SysUserDTO.QueryRequest request) {
        try {
            List<SysUserDTO> result = userAppService.getUserList(request);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("查询用户列表失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    @GetMapping("/enabled")
    @Operation(summary = "查询启用用户", description = "查询所有启用状态的用户")
    @PreAuthorize("hasAuthority('ROLE_ADMIN') or hasAuthority('system:user:list')")
    public ResponseEntity<List<SysUserDTO>> getAllEnabledUsers() {
        try {
            List<SysUserDTO> result = userAppService.getAllEnabledUsers();
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("查询启用用户失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    @PutMapping("/{id}/password")
    @Operation(summary = "修改密码", description = "用户修改自己的密码")
    @PreAuthorize("hasAuthority('ROLE_ADMIN') or hasAuthority('system:user:edit')")
    public ResponseEntity<Void> changePassword(
            @Parameter(description = "用户ID") @PathVariable Integer id,
            @Valid @RequestBody SysUserDTO.ChangePasswordRequest request) {
        try {
            request.setId(id);
            boolean result = userAppService.changePassword(request);
            return result ? ResponseEntity.ok().build() : ResponseEntity.badRequest().build();
        } catch (Exception e) {
            log.error("修改密码失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    @PutMapping("/{id}/reset-password")
    @Operation(summary = "重置密码", description = "管理员重置用户密码")
    @PreAuthorize("hasAuthority('ROLE_ADMIN')")
    public ResponseEntity<Void> resetPassword(
            @Parameter(description = "用户ID") @PathVariable Integer id,
            @Parameter(description = "新密码") @RequestParam String newPassword) {
        try {
            boolean result = userAppService.resetPassword(id, newPassword);
            return result ? ResponseEntity.ok().build() : ResponseEntity.badRequest().build();
        } catch (Exception e) {
            log.error("重置密码失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    @PutMapping("/{id}/status")
    @Operation(summary = "更新用户状态", description = "启用或禁用用户")
    @PreAuthorize("hasAuthority('ROLE_ADMIN') or hasAuthority('system:user:edit')")
    public ResponseEntity<Void> updateUserStatus(
            @Parameter(description = "用户ID") @PathVariable Integer id,
            @Parameter(description = "状态(0:禁用 1:启用)") @RequestParam Integer status) {
        try {
            boolean result = userAppService.updateUserStatus(id, status);
            return result ? ResponseEntity.ok().build() : ResponseEntity.badRequest().build();
        } catch (Exception e) {
            log.error("更新用户状态失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    @PutMapping("/{id}/roles")
    @Operation(summary = "分配角色", description = "为用户分配角色")
    @PreAuthorize("hasAuthority('ROLE_ADMIN') or hasAuthority('system:user:edit')")
    public ResponseEntity<Void> assignRoles(
            @Parameter(description = "用户ID") @PathVariable Integer id,
            @Parameter(description = "角色ID列表") @RequestBody List<Integer> roleIds) {
        try {
            boolean result = userAppService.assignRoles(id, roleIds);
            return result ? ResponseEntity.ok().build() : ResponseEntity.badRequest().build();
        } catch (Exception e) {
            log.error("分配角色失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    @PutMapping("/{id}/unlock")
    @Operation(summary = "解锁用户", description = "解锁被锁定的用户账户")
    @PreAuthorize("hasAuthority('ROLE_ADMIN')")
    public ResponseEntity<Void> unlockUser(@Parameter(description = "用户ID") @PathVariable Integer id) {
        try {
            boolean result = userAppService.unlockUser(id);
            return result ? ResponseEntity.ok().build() : ResponseEntity.badRequest().build();
        } catch (Exception e) {
            log.error("解锁用户失败", e);
            return ResponseEntity.badRequest().build();
        }
    }
}
