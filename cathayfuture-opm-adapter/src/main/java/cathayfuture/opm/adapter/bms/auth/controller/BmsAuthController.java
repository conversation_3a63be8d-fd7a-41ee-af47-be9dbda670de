package cathayfuture.opm.adapter.bms.auth.controller;

import cathayfuture.opm.adapter.bms.auth.dto.BmsLoginRequest;
import cathayfuture.opm.adapter.bms.auth.dto.BmsLoginResponse;
import cathayfuture.opm.adapter.mobile.utils.JwtUtils;
import cathayfuture.opm.adapter.security.TokenManagementService;
import com.alibaba.fastjson.JSON;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/bms/auth")
public class BmsAuthController {

    private final AuthenticationManager authenticationManager;

    @Resource
    private TokenManagementService tokenManagementService;

    public BmsAuthController(AuthenticationManager authenticationManager) {
        this.authenticationManager = authenticationManager;
    }
    
    @PostMapping("/login")
    public ResponseEntity<BmsLoginResponse> login(@RequestBody BmsLoginRequest request) {
        try {
            Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(request.getUsername(), request.getPassword())
            );
            
            // 创建token payload
            Map<String, Object> payload = new HashMap<>();
            payload.put("username", request.getUsername());
            payload.put("userId", 1L);
            payload.put("authorities", getAuthorities(authentication));
            
            // 生成JWT token
            String token = JwtUtils.createToken(payload);

            // 将token存储到Redis
            String userInfoJson = JSON.toJSONString(payload);
            boolean stored = tokenManagementService.storeToken(token, request.getUsername(), userInfoJson);

            if (!stored) {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(BmsLoginResponse.error("登录失败，请重试"));
            }

            // 生成refresh token
            String refreshToken = tokenManagementService.generateRefreshToken(request.getUsername());

            return ResponseEntity.ok(BmsLoginResponse.builder()
                .token(token)
                .refreshToken(refreshToken)
                .username(request.getUsername())
                .authorities(getAuthorities(authentication))
                .build());
                
        } catch (AuthenticationException e) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                .body(BmsLoginResponse.error("用户名或密码错误"));
        }
    }
    
    @PostMapping("/logout")
    public ResponseEntity<Void> logout(HttpServletRequest request) {
        // 从请求中提取token
        String token = extractTokenFromRequest(request);

        if (token != null) {
            // 从Redis中删除token
            boolean invalidated = tokenManagementService.invalidateToken(token);
            if (invalidated) {
                SecurityContextHolder.clearContext();
                return ResponseEntity.ok().build();
            } else {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
            }
        }

        SecurityContextHolder.clearContext();
        return ResponseEntity.ok().build();
    }

    @PostMapping("/refresh")
    public ResponseEntity<BmsLoginResponse> refreshToken(@RequestBody Map<String, String> request) {
        String refreshToken = request.get("refreshToken");

        if (refreshToken == null) {
            return ResponseEntity.badRequest()
                .body(BmsLoginResponse.error("Refresh token不能为空"));
        }

        // 验证refresh token
        String username = tokenManagementService.validateRefreshToken(refreshToken);
        if (username == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                .body(BmsLoginResponse.error("Refresh token无效或已过期"));
        }

        // 生成新的access token
        Map<String, Object> payload = new HashMap<>();
        payload.put("username", username);
        payload.put("userId", 1L);
        payload.put("authorities", Arrays.asList("ROLE_ADMIN")); // 这里应该从数据库获取实际权限

        String newToken = JwtUtils.createToken(payload);
        String userInfoJson = JSON.toJSONString(payload);

        // 存储新token到Redis
        boolean stored = tokenManagementService.storeToken(newToken, username, userInfoJson);
        if (!stored) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(BmsLoginResponse.error("Token刷新失败"));
        }

        // 生成新的refresh token
        String newRefreshToken = tokenManagementService.generateRefreshToken(username);

        // 使旧的refresh token失效
        tokenManagementService.invalidateRefreshToken(refreshToken);

        return ResponseEntity.ok(BmsLoginResponse.builder()
            .token(newToken)
            .refreshToken(newRefreshToken)
            .username(username)
            .authorities(Arrays.asList("ROLE_ADMIN"))
            .build());
    }

    /**
     * 从请求中提取token
     */
    private String extractTokenFromRequest(HttpServletRequest request) {
        String header = request.getHeader("Authorization");
        if (header != null && header.startsWith("Bearer ")) {
            return header.substring(7);
        }
        return header;
    }
    
    private List<String> getAuthorities(Authentication authentication) {
        return authentication.getAuthorities().stream()
            .map(GrantedAuthority::getAuthority)
            .collect(Collectors.toList());
    }
}