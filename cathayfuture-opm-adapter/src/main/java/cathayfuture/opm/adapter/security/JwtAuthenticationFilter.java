package cathayfuture.opm.adapter.security;

import cathayfuture.opm.adapter.mobile.utils.JwtUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.annotation.Resource;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    private static final Logger logger = LoggerFactory.getLogger(JwtAuthenticationFilter.class);

    @Resource
    private TokenManagementService tokenManagementService;

    @Override
    protected void doFilterInternal(HttpServletRequest request,
                                    HttpServletResponse response,
                                    FilterChain filterChain) throws ServletException, IOException {

        String token = extractTokenFromRequest(request);
        if (StringUtils.hasText(token)) {
            try {
                // 1. 首先验证JWT token的签名和过期时间
                if (JwtUtils.verifyToken(token)) {
                    // 2. 验证token是否在Redis中存在（未被登出）
                    if (tokenManagementService.isTokenValid(token)) {
                        // 3. 解析token中的用户信息
                        String userJson = JwtUtils.getClaimFromToken(token, "user");
                        if (StringUtils.hasText(userJson)) {
                            JSONObject userInfo = JSON.parseObject(userJson);
                            String username = userInfo.getString("username");
                            List<String> authoritiesStr = userInfo.getJSONArray("authorities").toJavaList(String.class);

                            // 创建权限列表
                            List<SimpleGrantedAuthority> authorities = new ArrayList<>();
                            for (String auth : authoritiesStr) {
                                authorities.add(new SimpleGrantedAuthority(auth));
                            }

                            // 创建Authentication对象
                            JwtAuthenticationToken authentication =
                                new JwtAuthenticationToken(username, token, authorities);
                            SecurityContextHolder.getContext().setAuthentication(authentication);

                            logger.debug("JWT认证成功，用户: {}, 权限: {}", username, authoritiesStr);
                        }
                    } else {
                        logger.debug("Token在Redis中不存在或已失效");
                    }
                } else {
                    logger.debug("JWT token签名验证失败");
                }
            } catch (Exception e) {
                logger.warn("JWT token验证失败: {}", e.getMessage());
            }
        }

        filterChain.doFilter(request, response);
    }
    
    private String extractTokenFromRequest(HttpServletRequest request) {
        String header = request.getHeader("Authorization");
        if (StringUtils.hasText(header) && header.startsWith("Bearer ")) {
            return header.substring(7);
        }
        return header;
    }
    
    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) {
        String uri = request.getRequestURI();
        // 跳过登录接口和公开接口
        if (uri.startsWith("/bms/auth/") || uri.startsWith("/mobile/wx/login") ||
            uri.startsWith("/mobile/callbacks/") || uri.startsWith("/actuator/") ||
            uri.startsWith("/swagger-") || uri.startsWith("/v3/api-docs") ||
            uri.equals("/swagger-ui.html") || uri.startsWith("/webjars/")) {
            return true;
        }
        // 对其他路径进行JWT验证
        return false;
    }
}