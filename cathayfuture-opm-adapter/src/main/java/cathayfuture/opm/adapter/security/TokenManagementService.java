package cathayfuture.opm.adapter.security;

import com.dtyunxi.huieryun.cache.api.IRedisCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

/**
 * Token管理服务
 * 负责JWT token的Redis存储、验证和失效管理
 * 
 * <AUTHOR>
 * @since 2025-08-12
 */
@Slf4j
@Service
public class TokenManagementService {
    
    private static final String TOKEN_CACHE_GROUP = "JWT_TOKEN_CACHE";
    private static final String REFRESH_TOKEN_CACHE_GROUP = "JWT_REFRESH_TOKEN_CACHE";
    private static final String USER_TOKEN_MAPPING_GROUP = "USER_TOKEN_MAPPING";
    
    // Token有效期：7天（与JWT中的过期时间保持一致）
    private static final int TOKEN_EXPIRE_HOURS = 7 * 24;
    // Refresh Token有效期：30天
    private static final int REFRESH_TOKEN_EXPIRE_DAYS = 30;
    
    @Resource
    private IRedisCacheService cacheService;
    
    /**
     * 存储token到Redis
     * @param token JWT token
     * @param username 用户名
     * @param userInfo 用户信息JSON字符串
     * @return 是否存储成功
     */
    public boolean storeToken(String token, String username, String userInfo) {
        try {
            // 1. 存储token信息
            TokenInfo tokenInfo = new TokenInfo();
            tokenInfo.setToken(token);
            tokenInfo.setUsername(username);
            tokenInfo.setUserInfo(userInfo);
            tokenInfo.setCreateTime(System.currentTimeMillis());
            tokenInfo.setLastAccessTime(System.currentTimeMillis());
            
            // 2. 将token存储到Redis，设置过期时间
            cacheService.setCache(TOKEN_CACHE_GROUP, token, tokenInfo, TOKEN_EXPIRE_HOURS * 3600);
            
            // 3. 建立用户名到token的映射（用于登出时查找用户的所有token）
            cacheService.setCache(USER_TOKEN_MAPPING_GROUP, username, token, TOKEN_EXPIRE_HOURS * 3600);
            
            log.debug("Token存储成功: username={}, token前缀={}", username, token.substring(0, Math.min(20, token.length())));
            return true;
        } catch (Exception e) {
            log.error("Token存储失败: username={}, error={}", username, e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 验证token是否有效
     * @param token JWT token
     * @return 是否有效
     */
    public boolean isTokenValid(String token) {
        if (!StringUtils.hasText(token)) {
            return false;
        }
        
        try {
            TokenInfo tokenInfo = cacheService.getCache(TOKEN_CACHE_GROUP, token, TokenInfo.class);
            if (tokenInfo == null) {
                log.debug("Token在Redis中不存在: {}", token.substring(0, Math.min(20, token.length())));
                return false;
            }
            
            // 更新最后访问时间
            tokenInfo.setLastAccessTime(System.currentTimeMillis());
            cacheService.setCache(TOKEN_CACHE_GROUP, token, tokenInfo, TOKEN_EXPIRE_HOURS * 3600);
            
            log.debug("Token验证成功: username={}", tokenInfo.getUsername());
            return true;
        } catch (Exception e) {
            log.error("Token验证失败: error={}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 获取token信息
     * @param token JWT token
     * @return token信息
     */
    public TokenInfo getTokenInfo(String token) {
        if (!StringUtils.hasText(token)) {
            return null;
        }
        
        try {
            return cacheService.getCache(TOKEN_CACHE_GROUP, token, TokenInfo.class);
        } catch (Exception e) {
            log.error("获取Token信息失败: error={}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 使token失效（登出）
     * @param token JWT token
     * @return 是否成功
     */
    public boolean invalidateToken(String token) {
        if (!StringUtils.hasText(token)) {
            return false;
        }
        
        try {
            // 1. 获取token信息
            TokenInfo tokenInfo = cacheService.getCache(TOKEN_CACHE_GROUP, token, TokenInfo.class);
            if (tokenInfo != null) {
                // 2. 删除用户名到token的映射
                cacheService.delCache(USER_TOKEN_MAPPING_GROUP, tokenInfo.getUsername());
                log.debug("删除用户token映射: username={}", tokenInfo.getUsername());
            }
            
            // 3. 删除token
            cacheService.delCache(TOKEN_CACHE_GROUP, token);
            
            log.info("Token失效成功: token前缀={}", token.substring(0, Math.min(20, token.length())));
            return true;
        } catch (Exception e) {
            log.error("Token失效失败: error={}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 使用户的所有token失效（强制登出）
     * @param username 用户名
     * @return 是否成功
     */
    public boolean invalidateUserTokens(String username) {
        if (!StringUtils.hasText(username)) {
            return false;
        }
        
        try {
            // 1. 获取用户当前的token
            String currentToken = cacheService.getCache(USER_TOKEN_MAPPING_GROUP, username, String.class);
            if (StringUtils.hasText(currentToken)) {
                // 2. 删除token
                cacheService.delCache(TOKEN_CACHE_GROUP, currentToken);
                log.debug("删除用户token: username={}, token前缀={}", username, currentToken.substring(0, Math.min(20, currentToken.length())));
            }
            
            // 3. 删除用户名到token的映射
            cacheService.delCache(USER_TOKEN_MAPPING_GROUP, username);
            
            log.info("用户所有Token失效成功: username={}", username);
            return true;
        } catch (Exception e) {
            log.error("用户Token失效失败: username={}, error={}", username, e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 生成refresh token
     * @param username 用户名
     * @return refresh token
     */
    public String generateRefreshToken(String username) {
        try {
            String refreshToken = "refresh_" + System.currentTimeMillis() + "_" + username.hashCode();
            
            // 存储refresh token，设置30天过期
            RefreshTokenInfo refreshTokenInfo = new RefreshTokenInfo();
            refreshTokenInfo.setRefreshToken(refreshToken);
            refreshTokenInfo.setUsername(username);
            refreshTokenInfo.setCreateTime(System.currentTimeMillis());
            
            cacheService.setCache(REFRESH_TOKEN_CACHE_GROUP, refreshToken, refreshTokenInfo, REFRESH_TOKEN_EXPIRE_DAYS * 24 * 3600);
            
            log.debug("Refresh Token生成成功: username={}", username);
            return refreshToken;
        } catch (Exception e) {
            log.error("Refresh Token生成失败: username={}, error={}", username, e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 验证refresh token
     * @param refreshToken refresh token
     * @return 用户名，如果无效返回null
     */
    public String validateRefreshToken(String refreshToken) {
        if (!StringUtils.hasText(refreshToken)) {
            return null;
        }
        
        try {
            RefreshTokenInfo refreshTokenInfo = cacheService.getCache(REFRESH_TOKEN_CACHE_GROUP, refreshToken, RefreshTokenInfo.class);
            if (refreshTokenInfo != null) {
                log.debug("Refresh Token验证成功: username={}", refreshTokenInfo.getUsername());
                return refreshTokenInfo.getUsername();
            }
            
            log.debug("Refresh Token无效或已过期");
            return null;
        } catch (Exception e) {
            log.error("Refresh Token验证失败: error={}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 使refresh token失效
     * @param refreshToken refresh token
     * @return 是否成功
     */
    public boolean invalidateRefreshToken(String refreshToken) {
        if (!StringUtils.hasText(refreshToken)) {
            return false;
        }
        
        try {
            cacheService.delCache(REFRESH_TOKEN_CACHE_GROUP, refreshToken);
            log.debug("Refresh Token失效成功");
            return true;
        } catch (Exception e) {
            log.error("Refresh Token失效失败: error={}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * Token信息内部类
     */
    public static class TokenInfo {
        private String token;
        private String username;
        private String userInfo;
        private long createTime;
        private long lastAccessTime;

        // Getters and Setters
        public String getToken() { return token; }
        public void setToken(String token) { this.token = token; }

        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }

        public String getUserInfo() { return userInfo; }
        public void setUserInfo(String userInfo) { this.userInfo = userInfo; }

        public long getCreateTime() { return createTime; }
        public void setCreateTime(long createTime) { this.createTime = createTime; }

        public long getLastAccessTime() { return lastAccessTime; }
        public void setLastAccessTime(long lastAccessTime) { this.lastAccessTime = lastAccessTime; }
    }
    
    /**
     * Refresh Token信息内部类
     */
    public static class RefreshTokenInfo {
        private String refreshToken;
        private String username;
        private long createTime;
        
        // Getters and Setters
        public String getRefreshToken() { return refreshToken; }
        public void setRefreshToken(String refreshToken) { this.refreshToken = refreshToken; }
        
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        
        public long getCreateTime() { return createTime; }
        public void setCreateTime(long createTime) { this.createTime = createTime; }
    }
}
