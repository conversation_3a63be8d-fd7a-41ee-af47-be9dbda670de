package cathayfuture.opm.adapter.security;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

@Slf4j
@Configuration
@EnableWebSecurity
public class SecurityConfig extends WebSecurityConfigurerAdapter {

    private final DatabaseUserDetailsService databaseUserDetailsService;
    private final FixedUserDetailsService fixedUserDetailsService;
    private final JwtAuthenticationFilter jwtAuthenticationFilter;

    @Value("${cathay.security.dev-mode:false}")
    private boolean devMode;

    @Value("${cathay.security.use-database-auth:true}")
    private boolean useDatabaseAuth;

    public SecurityConfig(DatabaseUserDetailsService databaseUserDetailsService,
                         FixedUserDetailsService fixedUserDetailsService,
                         JwtAuthenticationFilter jwtAuthenticationFilter) {
        this.databaseUserDetailsService = databaseUserDetailsService;
        this.fixedUserDetailsService = fixedUserDetailsService;
        this.jwtAuthenticationFilter = jwtAuthenticationFilter;
    }
    
    @Override
    protected void configure(HttpSecurity http) throws Exception {
        if (devMode) {
            // 开发模式：允许所有接口无认证访问
            http
                .sessionManagement()
                    .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
                .and()
                .authorizeRequests()
                    .anyRequest().permitAll()
                .and()
                .csrf().disable()
                .cors().disable();
        } else {
            // 生产模式：正常的认证配置
            http
                .sessionManagement()
                    .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
                .and()
                .authorizeRequests()
                    .antMatchers("/bms/auth/**").permitAll()
                    .antMatchers("/mobile/wx/login", "/mobile/callbacks/**").permitAll()
                    .antMatchers("/actuator/**").permitAll()
                    .antMatchers("/swagger-ui/**", "/v3/api-docs/**", "/swagger-ui.html", "/webjars/**").permitAll()
                    .antMatchers("/bms/**").authenticated()
                    .antMatchers("/mobile/**").authenticated()
                    .anyRequest().authenticated()
                .and()
                .authenticationProvider(daoAuthenticationProvider())
                .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class)
                .csrf().disable()
                .cors().disable();
        }
    }
    
    @Bean
    public DaoAuthenticationProvider daoAuthenticationProvider() {
        // 使用带日志的认证提供者进行调试
        LoggingAuthenticationProvider provider;

        // 根据配置选择使用数据库认证还是固定用户认证
        if (useDatabaseAuth) {
            log.info("配置数据库认证提供者");
            provider = new LoggingAuthenticationProvider(databaseUserDetailsService, passwordEncoder());
        } else {
            log.info("配置固定用户认证提供者");
            provider = new LoggingAuthenticationProvider(fixedUserDetailsService, passwordEncoder());
        }

        return provider;
    }
    
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
    
    @Bean
    @Override
    public AuthenticationManager authenticationManagerBean() throws Exception {
        return super.authenticationManagerBean();
    }
}