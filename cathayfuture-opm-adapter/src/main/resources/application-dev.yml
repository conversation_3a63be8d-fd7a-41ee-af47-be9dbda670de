management.health.redis.enabled: false
# 使用本地配置
# spring.cloud.config.allowOverride: true
# spring.cloud.config.overrideNone: true
# spring.cloud.config.overrideSystemProperties: false

spring:
  datasource:
    driver-class-name: com.mysql.jdbc.Driver
    url: *****************************************************************************************************************************************************************************************
    username: root
    password: f3l+Upvb
  main:
    allow-bean-definition-overriding: true


  jackson:
    serialization:
      write-dates-as-timestamps: true
    deserialization:
      fail_on_unknown_properties: false

huieryun.cacheregistryvo:
  port: 6379
  appId: ""
  livetime: 86370
  host: *********
  appSecret: "Tasly@2021"
  type: REDIS

huieryun.service.lock.lockregistryvo:
  provider: redis
  endpoints: *********:6379
  passwd: Tasly@2021

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

wx:
  miniapp:
    configs:
      - appid: wx3f126b1c4bc46dcf
        secret: 0eebded2c699174c6c30953541774d79
        token:
        aesKey:
        msgDataFormat: JSON

payment-center:
  user-no: CATHAYFUTURE
  pay-product-code: CATHAYFUTURE_PAYMENT
  pay-type-code: WX_JSAPI
  pay-way-code: WX
  pre-pay-url: https://mpgateway-dev.tasly.com/pay-center/payment/pre-order
  notify-url: https://mpgateway-dev.tasly.com/api/cathayfuture/mobile/callbacks/pay/notify
  query-pay-result-url: https://mpgateway-dev.tasly.com/pay-center/payment/wx/query-order/{tenantNo}/{tenantOrderNo}

# huieryun.ossregistryvo:
#   ossType: AmazonS3
#   endpoint: https://tsywisdom.i.tasly.com
#   accessKeyId: dHN5d2lzZG9t
#   accessKeySecret: bb6b96539b57b2279b5f02f0f0308a99
#   acl: public-read
#   extFields: {ossTenant: tsywisdom, anonUrl: https://api.i.tasly.com/swift/v1}
#   bucketName: prod
#   extProperty: {prod: prod}
#   dir: cathayfuture-opm/
# huieryun.ossappvo:
#   appId: tasly
huieryun.ossregistryvo:
  ossType: AmazonS3
  endpoint: https://b2b.i.tasly.com
  accessKeyId: IGIyYmRldjAx
  accessKeySecret: b047853fce4e4286bb5004d52c631928
  acl: public-read
  extFields: {ossTenant: b2b, anonUrl: https://api.i.tasly.com/swift/v1}
  bucketName: middle-platform-public-dev
  extProperty: {middle-platform-public-dev: middle-platform-public-dev}
  dir: tasly-center-rebate/
huieryun.ossappvo:
  appId: tasly

sms:
  ali:
    accessKeyId: LTAI4FzjFk5UNDpRZH3AUKbh
    accessKeySecret: ******************************
    signName: 天人智慧
    templateCode: SMS_170775265
    templateParamName: code
  send:
    profiles: prd, prod

order:
  code:
    prefix: CF_TEST
  frontMoney:
    amount: 0.01


# 固定用户配置
cathay:
  auth:
    # 新格式：多用户支持
    users:
      - username: admin
        password: admin123
        role: ROLE_ADMIN
        user-id: 1
        role-ids: "1,2,3"
        user-name: "系统管理员"
        post-code: "ADMIN"
        post-name: "超级管理员"
      - username: manager
        password: manager123
        role: ROLE_MANAGER
        user-id: 2
        role-ids: "2,3"
        user-name: "业务管理员"
        post-code: "MANAGER"
        post-name: "业务管理员"
    # 旧格式：向后兼容（默认使用admin用户）
    username: admin
    password: admin123
    user-id: 1
    role-ids: "1,2,3"
    user-name: "系统管理员"
    post-code: "ADMIN"
    post-name: "超级管理员"