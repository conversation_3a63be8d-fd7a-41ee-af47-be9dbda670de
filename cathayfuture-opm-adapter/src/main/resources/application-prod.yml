management.health.redis.enabled: false
spring:
  datasource:
    driver-class-name: com.mysql.jdbc.Driver
    url: *****************************************************************************************************************************************************************************************
    username: opm
    password: SH6RzSa#
  main:
    allow-bean-definition-overriding: true


  jackson:
    serialization:
      write-dates-as-timestamps: true
    deserialization:
      fail_on_unknown_properties: false

huieryun.cacheregistryvo:
  # port: 6379
  appId: ""
  livetime: 86370
  # host: *********
  addresses: **********:6379,**********:6379,**********:6379,**********:6379,**********:6379,**********:6379
  workModel: cluster
  appSecret: "Tasly_01!"
  type: REDIS

huieryun.service.lock.lockregistryvo:
  provider: redis
  endpoints: **********:6379,**********:6379,**********:6379,**********:6379,**********:6379,**********:6379
  workModel: cluster
  passwd: Tasly_01!

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

wx:
  miniapp:
    configs:
      - appid: wxb077691a604c20e6
        secret: ab4820f3df719d3b7e563e6880b79d8c
        token:
        aesKey:
        msgDataFormat: JSON

payment-center:
  user-no: CATHAYFUTURE
  pay-product-code: CATHAYFUTURE_PAYMENT
  pay-type-code: WX_JSAPI
  pay-way-code: WX
  pre-pay-url: https://mpgateway.tasly.com/pay-center/payment/pre-order
  notify-url: https://mpgateway.tasly.com/api/cathayfuture/mobile/callbacks/pay/notify
  query-pay-result-url: https://mpgateway.tasly.com/pay-center/payment/wx/query-order/{tenantNo}/{tenantOrderNo}

huieryun.ossregistryvo:
  ossType: AmazonS3
  endpoint: https://tsywisdom.i.tasly.com
  accessKeyId: dHN5d2lzZG9t
  accessKeySecret: bb6b96539b57b2279b5f02f0f0308a99
  acl: public-read
  extFields: {ossTenant: tsywisdom, anonUrl: https://api.i.tasly.com/swift/v1}
  bucketName: prod
  extProperty: {prod: prod}
  dir: cathayfuture-opm/
huieryun.ossappvo:
  appId: tsywisdom


sms:
  ali:
    accessKeyId: LTAI4FzjFk5UNDpRZH3AUKbh
    accessKeySecret: ******************************
    signName: 天人智慧
    templateCode: SMS_170775265
    templateParamName: code
  send:
    profiles: prd, prod

order:
  code:
    prefix: CF
  frontMoney:
    amount: 3000


ribbon:
  ReadTimeout: 200000
  ConnectTimeout: 200000
  ServerListRefreshInterval: 30000

# 固定用户配置
cathay:
  auth:
    # 新格式：多用户支持
    users:
      - username: admin
        password: admin123
        role: ROLE_ADMIN
        user-id: 1
        role-ids: "1,2,3"
        user-name: "系统管理员"
        post-code: "ADMIN"
        post-name: "超级管理员"
      - username: manager
        password: manager123
        role: ROLE_MANAGER
        user-id: 2
        role-ids: "2,3"
        user-name: "业务管理员"
        post-code: "MANAGER"
        post-name: "业务管理员"
    # 旧格式：向后兼容（默认使用admin用户）
    username: admin
    password: admin123
    user-id: 1
    role-ids: "1,2,3"
    user-name: "系统管理员"
    post-code: "ADMIN"
    post-name: "超级管理员"