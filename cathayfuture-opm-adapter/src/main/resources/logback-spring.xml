<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <!-- 读取spring 上下文环境信息-->
    <springProperty scope="context" name="dtyunxi.env.module" source="spring.application.name"
                    defaultValue="localhost"/>
    <springProperty scope="context" name="yes.log.version" source="yes.log.version" defaultValue="1.0"/>
    <springProperty scope="context" name="yes.server.logWorkDir" source="yes.server.logWorkDir" defaultValue="."/>
    <springProperty scope="context" name="dtyunxi.env.logger.level" source="dtyunxi.env.logger.level" defaultValue="INFO"/>
    <springProperty scope="context" name="yes.server.localHost" source="POD_IP" defaultValue="127.0.0.1"/>

    <!-- console日志 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%date %-5level [v:${yes.log.version}] [h:${yes.server.localHost}] [m:${dtyunxi.env.module}] [reqId:%X{yes.req.requestId}] [%thread] %logger{36} [L:%line] - %msg%n
            </pattern>
        </encoder>
    </appender>

    <appender name="ROLLING-INFO" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>
            ${yes.server.logWorkDir}/${dtyunxi.env.module}/${dtyunxi.env.module}.log
        </file>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>
                ${yes.server.logWorkDir}/${dtyunxi.env.module}/${dtyunxi.env.module}-%d{yyyy-MM-dd}.%i.log.gz
            </fileNamePattern>
            <maxFileSize>500MB</maxFileSize><!-- 单日志文件大小 -->
            <maxHistory>180</maxHistory> <!-- 保留180天日志 -->
            <totalSizeCap>20GB</totalSizeCap> <!-- 日志总量大小，超过该值旧的日志文件将被清理-->
        </rollingPolicy>
        <encoder>
            <pattern>%date %-5level [v:${yes.log.version}] [h:${yes.server.localHost}] [m:${dtyunxi.env.module}] [reqId:%X{yes.req.requestId}] [%thread] %logger{36} [L:%line] - %msg%n
            </pattern>
        </encoder>
    </appender>


    <appender name="ROLLING-DEBUG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>
            ${yes.server.logWorkDir}/${dtyunxi.env.module}/${dtyunxi.env.module}-debug.log
        </file>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>DEBUG</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>
                ${yes.server.logWorkDir}/${dtyunxi.env.module}/${dtyunxi.env.module}-debug-%d{yyyy-MM-dd}.%i.log
            </fileNamePattern>
            <maxFileSize>200MB</maxFileSize><!-- 单日志文件大小 -->
            <maxHistory>30</maxHistory> <!-- 保留30天日志 -->
            <totalSizeCap>20GB</totalSizeCap> <!-- 日志总量大小，超过该值旧的日志文件将被清理-->
        </rollingPolicy>
        <encoder>
            <pattern>%date %-5level [v:${yes.log.version}] [h:${yes.server.localHost}] [m:${dtyunxi.env.module}] [reqId:%X{yes.req.requestId}] [%thread] %logger{36} [L:%line] - %msg%n
            </pattern>
        </encoder>
    </appender>

    <appender name="ROLLING-WARN" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>
            ${yes.server.logWorkDir}/${dtyunxi.env.module}/${dtyunxi.env.module}-warn.log
        </file>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>WARN</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>
                ${yes.server.logWorkDir}/${dtyunxi.env.module}/${dtyunxi.env.module}-warn-%d{yyyy-MM-dd}.%i.log
            </fileNamePattern>
            <maxFileSize>200MB</maxFileSize><!-- 单日志文件大小 -->
            <maxHistory>180</maxHistory> <!-- 保留180天日志 -->
            <totalSizeCap>20GB</totalSizeCap> <!-- 日志总量大小，超过该值旧的日志文件将被清理-->
        </rollingPolicy>
        <encoder>
            <pattern>%date %-5level [v:${yes.log.version}] [h:${yes.server.localHost}] [m:${dtyunxi.env.module}] [reqId:%X{yes.req.requestId}] [%thread] %logger{36} [L:%line] - %msg%n
            </pattern>
        </encoder>
    </appender>

    <!-- project default level -->

    <logger name="org" level="INFO"/>
    <logger name="com.aliyuncs" level="ERROR"/>
    <logger name="com.alibaba" level="ERROR"/>
    <logger name="com.taobao" level="ERROR"/>
    <logger name="com.dtyunxi.tasly.center.dao.mapper" level="DEBUG"/>


    <springProfile name="dev,test,stage">
        <root level="${dtyunxi.env.logger.level}">
            <appender-ref ref="STDOUT"/>
            <appender-ref ref="ROLLING-INFO"/>
            <appender-ref ref="ROLLING-DEBUG"/>
            <appender-ref ref="ROLLING-WARN"/>
        </root>

        <!--log4jdbc -->
        <logger name="jdbc.sqltiming" level="debug"/>
        <logger name="com.ibatis" level="DEBUG"/>
        <logger name="com.ibatis.common.jdbc.SimpleDataSource" level="debug"/>
        <logger name="com.ibatis.common.jdbc.ScriptRunner" level="debug"/>
        <logger name="com.ibatis.sqlmap.engine.impl.SqlMapClientDelegate" level="debug"/>
        <logger name="java.sql.Connection" level="DEBUG"/>
        <logger name="java.sql.Statement" level="DEBUG"/>
        <logger name="java.sql.PreparedStatement" level="DEBUG"/>
        <logger name="com.hryt.charging" level="DEBUG"/>
    </springProfile>
    <springProfile name="prod">
        <root level="${dtyunxi.env.logger.level}">
            <appender-ref ref="STDOUT"/>
            <appender-ref ref="ROLLING-INFO"/>
        </root>
        <logger name="com.hryt.charging" level="INFO"/>
    </springProfile>

</configuration>
