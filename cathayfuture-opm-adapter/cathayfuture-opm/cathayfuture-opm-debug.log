2025-08-12 09:04:05,069 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-12 09:04:06,146 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-12 09:05:29,061 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.JwtAuthenticationFilter [L:55] - JWT认证成功，用户: admin, 权限: [ROLE_ADMIN]
2025-08-12 09:05:50,124 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.JwtAuthenticationFilter [L:55] - JWT认证成功，用户: admin, 权限: [ROLE_ADMIN]
2025-08-12 09:06:02,324 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.JwtAuthenticationFilter [L:55] - JWT认证成功，用户: admin, 权限: [ROLE_ADMIN]
2025-08-12 09:13:44,483 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-12 09:13:45,567 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-12 09:14:20,316 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.JwtAuthenticationFilter [L:55] - JWT认证成功，用户: admin, 权限: [ROLE_ADMIN]
2025-08-12 09:14:31,081 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.JwtAuthenticationFilter [L:55] - JWT认证成功，用户: admin, 权限: [ROLE_ADMIN]
2025-08-12 09:14:41,973 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.opm.adapter.mobile.utils.JwtUtils [L:114] - Token验证失败: The input is not a valid base 64 encoded string.
2025-08-12 09:18:58,721 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-12 09:18:59,771 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-12 09:20:22,213 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.JwtAuthenticationFilter [L:55] - JWT认证成功，用户: admin, 权限: [ROLE_ADMIN]
2025-08-12 09:20:39,567 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.JwtAuthenticationFilter [L:55] - JWT认证成功，用户: admin, 权限: [ROLE_ADMIN]
2025-08-12 09:20:59,312 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.JwtAuthenticationFilter [L:55] - JWT认证成功，用户: admin, 权限: [ROLE_ADMIN]
2025-08-12 09:21:58,039 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-12 09:21:59,045 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-12 09:24:26,667 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.JwtAuthenticationFilter [L:55] - JWT认证成功，用户: admin, 权限: [ROLE_ADMIN]
2025-08-12 09:25:28,570 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.JwtAuthenticationFilter [L:55] - JWT认证成功，用户: admin, 权限: [ROLE_ADMIN]
2025-08-12 09:25:38,624 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.opm.adapter.mobile.utils.JwtUtils [L:114] - Token验证失败: The token was expected to have 3 parts, but got 1.
2025-08-12 09:28:36,141 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.JwtAuthenticationFilter [L:55] - JWT认证成功，用户: admin, 权限: [ROLE_ADMIN]
2025-08-12 09:30:00,684 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.JwtAuthenticationFilter [L:55] - JWT认证成功，用户: admin, 权限: [ROLE_ADMIN]
2025-08-12 09:49:09,074 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-12 09:49:10,137 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-12 09:49:47,227 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.TokenManagementService [L:59] - Token存储成功: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-12 09:49:47,229 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.TokenManagementService [L:191] - Refresh Token生成成功: username=admin
2025-08-12 09:50:03,984 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.TokenManagementService [L:80] - Token在Redis中不存在: eyJ0eXAiOiJKV1QiLCJh
2025-08-12 09:50:03,984 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.JwtAuthenticationFilter [L:64] - Token在Redis中不存在或已失效
2025-08-12 09:53:41,222 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-12 09:53:42,279 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-12 09:54:00,817 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.TokenManagementService [L:56] - Token存储成功: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-12 09:54:00,819 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.TokenManagementService [L:188] - Refresh Token生成成功: username=admin
2025-08-12 09:54:19,107 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:85] - Token验证成功: username=admin
2025-08-12 09:54:19,112 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.JwtAuthenticationFilter [L:61] - JWT认证成功，用户: admin, 权限: [ROLE_ADMIN]
2025-08-12 09:54:36,620 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.TokenManagementService [L:127] - 删除用户token映射: username=admin
2025-08-12 09:54:51,287 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.TokenManagementService [L:77] - Token在Redis中不存在: eyJ0eXAiOiJKV1QiLCJh
2025-08-12 09:54:51,287 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.JwtAuthenticationFilter [L:64] - Token在Redis中不存在或已失效
2025-08-12 10:30:38,931 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-12 10:30:40,000 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-12 10:32:47,497 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.TokenManagementService [L:56] - Token存储成功: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-12 10:32:47,501 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.TokenManagementService [L:188] - Refresh Token生成成功: username=admin
2025-08-12 10:34:11,453 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.TokenManagementService [L:85] - Token验证成功: username=admin
2025-08-12 10:34:11,460 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.JwtAuthenticationFilter [L:61] - JWT认证成功，用户: admin, 权限: [ROLE_ADMIN]
2025-08-12 10:39:32,364 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-12 10:39:33,439 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-12 14:58:42,761 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-12 14:58:43,839 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
