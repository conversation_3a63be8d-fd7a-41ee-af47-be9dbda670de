<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>cathayfuture-opm</artifactId>
    <groupId>cathayfuture.opm</groupId>
    <version>1.0-SNAPSHOT</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>

  <artifactId>cathayfuture-opm-app</artifactId>

  <dependencies>
    <dependency>
      <groupId>cathayfuture.opm</groupId>
      <artifactId>cathayfuture-opm-client</artifactId>
      <version>${parent.version}</version>
    </dependency>

    <dependency>
      <groupId>cathayfuture.opm</groupId>
      <artifactId>cathayfuture-opm-domain</artifactId>
      <version>${parent.version}</version>
    </dependency>

    <dependency>
      <groupId>cathayfuture.opm</groupId>
      <artifactId>cathayfuture-opm-infra</artifactId>
      <version>${parent.version}</version>
    </dependency>

    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-context</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-tx</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-cache</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-data-redis</artifactId>
    </dependency>
    <dependency>
      <groupId>com.dtyunxi.cube</groupId>
      <artifactId>cube-starter-lock</artifactId>
    </dependency>
    <dependency>
      <groupId>com.dtyunxi.huieryun</groupId>
      <artifactId>huieryun-lockredis</artifactId>
    </dependency>
    <dependency>
      <groupId>com.taslyware</groupId>
      <artifactId>taslyware-excel-tools</artifactId>
    </dependency>
    <dependency>
      <groupId>com.dtyunxi.huieryun</groupId>
      <artifactId>huieryun-starter-objectstorage</artifactId>
    </dependency>
    <dependency>
      <groupId>com.dtyunxi.cube</groupId>
      <artifactId>cube-starter-cache</artifactId>
    </dependency>
    <dependency>
      <groupId>redis.clients</groupId>
      <artifactId>jedis</artifactId>
    </dependency>
  </dependencies>

</project>
