package cathayfuture.opm.app.common;

import cathayfuture.opm.client.common.IdSequenceService;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.support.atomic.RedisAtomicLong;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 描述：
 *
 * <AUTHOR>
 * @date 2022/8/27
 */
@Service
public class IdSequenceServiceRedisImpl implements IdSequenceService {

    private RedisTemplate<String, Object> redisTemplate;

    private RedisConnectionFactory redisConnectionFactory;

    public IdSequenceServiceRedisImpl(RedisTemplate redisTemplate, RedisConnectionFactory redisConnectionFactory) {
        this.redisTemplate = redisTemplate;
        this.redisConnectionFactory = redisConnectionFactory;
    }

    @Override
    public Integer incrAndGetSequence(String prefix) {
        long defaultLiveTime = 24;
        if (null == redisConnectionFactory) {
            redisConnectionFactory = redisTemplate.getConnectionFactory();
        }
        Objects.requireNonNull(redisConnectionFactory);
        RedisAtomicLong entityIdCounter = new RedisAtomicLong(prefix, redisConnectionFactory);
        long increment = entityIdCounter.incrementAndGet();
        if (increment == 0) {
            entityIdCounter.expire(defaultLiveTime, TimeUnit.HOURS);
        }
        return (int) increment;
    }

    @Override
    public Integer incrAndGetSequence(String prefix, Integer increment) {
        if (increment < 0) {
            increment = 1;
        }
        if (null == redisConnectionFactory) {
            redisConnectionFactory = redisTemplate.getConnectionFactory();
        }
        long result = 0L;
        for (int i = 0; i < increment; i++) {
            result = incrAndGetSequence(prefix);
        }
        return (int) result;
    }
}
