package cathayfuture.opm.app.system.service;

import cathayfuture.opm.client.system.dto.SysUserDTO;
import cathayfuture.opm.domain.system.SysUserEntity;
import cathayfuture.opm.domain.system.SysRoleEntity;
import cathayfuture.opm.domain.system.repository.SysUserRepository;
import cathayfuture.opm.domain.system.repository.SysRoleRepository;
import cathayfuture.opm.domain.system.repository.SysUserRoleRepository;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 系统用户应用服务实现
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@Slf4j
@Service
public class SysUserAppServiceImpl implements SysUserAppService {

    private final SysUserRepository userRepository;
    private final SysRoleRepository roleRepository;
    private final SysUserRoleRepository userRoleRepository;
    private final PasswordEncoder passwordEncoder;

    public SysUserAppServiceImpl(SysUserRepository userRepository,
                                SysRoleRepository roleRepository,
                                SysUserRoleRepository userRoleRepository,
                                PasswordEncoder passwordEncoder) {
        this.userRepository = userRepository;
        this.roleRepository = roleRepository;
        this.userRoleRepository = userRoleRepository;
        this.passwordEncoder = passwordEncoder;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SysUserDTO createUser(SysUserDTO.CreateRequest request) {
        log.info("创建用户: {}", request.getUsername());
        
        // 1. 检查用户名是否已存在
        if (userRepository.existsByUsername(request.getUsername(), null)) {
            throw new RuntimeException("用户名已存在: " + request.getUsername());
        }

        // 2. 检查用户编码是否已存在
        if (StringUtils.hasText(request.getUserCode()) &&
            userRepository.existsByUserCode(request.getUserCode(), null)) {
            throw new RuntimeException("用户编码已存在: " + request.getUserCode());
        }

        // 3. 检查邮箱是否已存在
        if (StringUtils.hasText(request.getEmail()) &&
            userRepository.existsByEmail(request.getEmail(), null)) {
            throw new RuntimeException("邮箱已存在: " + request.getEmail());
        }
        
        // 3. 创建用户实体
        SysUserEntity userEntity = new SysUserEntity();
        BeanUtils.copyProperties(request, userEntity);
        userEntity.setPassword(passwordEncoder.encode(request.getPassword()));
        userEntity.setPasswordUpdateTime(LocalDateTime.now());
        userEntity.setLoginFailureCount(0);
        
        // 4. 保存用户
        userEntity = userRepository.save(userEntity);
        
        // 5. 分配角色
        if (!CollectionUtils.isEmpty(request.getRoleIds())) {
            userRoleRepository.assignRolesToUser(userEntity.getId(), request.getRoleIds());
        }
        
        log.info("用户创建成功: id={}, username={}", userEntity.getId(), userEntity.getUsername());
        return convertToDTO(userEntity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SysUserDTO updateUser(SysUserDTO.UpdateRequest request) {
        log.info("更新用户: id={}", request.getId());
        
        // 1. 查询用户是否存在
        SysUserEntity userEntity = userRepository.findById(request.getId());
        if (userEntity == null) {
            throw new RuntimeException("用户不存在: " + request.getId());
        }
        
        // 2. 检查邮箱是否已被其他用户使用
        if (StringUtils.hasText(request.getEmail()) && 
            userRepository.existsByEmail(request.getEmail(), request.getId())) {
            throw new RuntimeException("邮箱已被其他用户使用: " + request.getEmail());
        }
        
        // 3. 更新用户信息
        if (StringUtils.hasText(request.getRealName())) {
            userEntity.setRealName(request.getRealName());
        }
        if (StringUtils.hasText(request.getOaName())) {
            userEntity.setOaName(request.getOaName());
        }
        if (StringUtils.hasText(request.getEmail())) {
            userEntity.setEmail(request.getEmail());
        }
        if (StringUtils.hasText(request.getPhone())) {
            userEntity.setPhone(request.getPhone());
        }
        if (StringUtils.hasText(request.getAvatar())) {
            userEntity.setAvatar(request.getAvatar());
        }
        if (request.getStatus() != null) {
            userEntity.setStatus(request.getStatus());
        }
        
        // 4. 保存用户
        userRepository.updateById(userEntity);
        
        // 5. 更新角色分配
        if (request.getRoleIds() != null) {
            userRoleRepository.assignRolesToUser(userEntity.getId(), request.getRoleIds());
        }
        
        log.info("用户更新成功: id={}, username={}", userEntity.getId(), userEntity.getUsername());
        return convertToDTO(userEntity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteUser(Integer id) {
        log.info("删除用户: id={}", id);
        
        // 1. 检查用户是否存在
        SysUserEntity userEntity = userRepository.findById(id);
        if (userEntity == null) {
            throw new RuntimeException("用户不存在: " + id);
        }
        
        // 2. 删除用户角色关联
        userRoleRepository.deleteByUserId(id);
        
        // 3. 删除用户（逻辑删除）
        boolean result = userRepository.deleteById(id);
        
        log.info("用户删除成功: id={}, username={}", id, userEntity.getUsername());
        return result;
    }

    @Override
    public SysUserDTO getUserById(Integer id) {
        SysUserEntity userEntity = userRepository.findById(id);
        if (userEntity == null) {
            return null;
        }
        return convertToDTO(userEntity);
    }

    @Override
    public List<SysUserDTO> getUserList(SysUserDTO.QueryRequest request) {
        List<SysUserEntity> entities = userRepository.findByCondition(
                request.getUsername(), request.getUserCode(), request.getRealName(), request.getOaName(), request.getStatus());

        return entities.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<SysUserDTO> getAllEnabledUsers() {
        List<SysUserEntity> entities = userRepository.findAllEnabled();
        return entities.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean changePassword(SysUserDTO.ChangePasswordRequest request) {
        log.info("修改密码: id={}", request.getId());
        
        // 1. 查询用户
        SysUserEntity userEntity = userRepository.findById(request.getId());
        if (userEntity == null) {
            throw new RuntimeException("用户不存在: " + request.getId());
        }
        
        // 2. 验证旧密码
        if (!passwordEncoder.matches(request.getOldPassword(), userEntity.getPassword())) {
            throw new RuntimeException("旧密码不正确");
        }
        
        // 3. 更新密码
        userEntity.setPassword(passwordEncoder.encode(request.getNewPassword()));
        userEntity.setPasswordUpdateTime(LocalDateTime.now());
        
        boolean result = userRepository.updateById(userEntity);
        log.info("密码修改成功: id={}, username={}", userEntity.getId(), userEntity.getUsername());
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean resetPassword(Integer id, String newPassword) {
        log.info("重置密码: id={}", id);
        
        // 1. 查询用户
        SysUserEntity userEntity = userRepository.findById(id);
        if (userEntity == null) {
            throw new RuntimeException("用户不存在: " + id);
        }
        
        // 2. 重置密码
        userEntity.setPassword(passwordEncoder.encode(newPassword));
        userEntity.setPasswordUpdateTime(LocalDateTime.now());
        userEntity.setLoginFailureCount(0); // 重置登录失败次数
        userEntity.setAccountLockedTime(null); // 解除账户锁定
        
        boolean result = userRepository.updateById(userEntity);
        log.info("密码重置成功: id={}, username={}", userEntity.getId(), userEntity.getUsername());
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUserStatus(Integer id, Integer status) {
        log.info("更新用户状态: id={}, status={}", id, status);
        
        SysUserEntity userEntity = userRepository.findById(id);
        if (userEntity == null) {
            throw new RuntimeException("用户不存在: " + id);
        }
        
        userEntity.setStatus(status);
        boolean result = userRepository.updateById(userEntity);
        
        log.info("用户状态更新成功: id={}, username={}, status={}", 
                userEntity.getId(), userEntity.getUsername(), status);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean assignRoles(Integer userId, List<Integer> roleIds) {
        log.info("为用户分配角色: userId={}, roleIds={}", userId, roleIds);
        
        // 检查用户是否存在
        SysUserEntity userEntity = userRepository.findById(userId);
        if (userEntity == null) {
            throw new RuntimeException("用户不存在: " + userId);
        }
        
        boolean result = userRoleRepository.assignRolesToUser(userId, roleIds);
        log.info("角色分配成功: userId={}, roleIds={}", userId, roleIds);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean unlockUser(Integer id) {
        log.info("解锁用户: id={}", id);
        
        SysUserEntity userEntity = userRepository.findById(id);
        if (userEntity == null) {
            throw new RuntimeException("用户不存在: " + id);
        }
        
        userEntity.setLoginFailureCount(0);
        userEntity.setAccountLockedTime(null);
        
        boolean result = userRepository.updateById(userEntity);
        log.info("用户解锁成功: id={}, username={}", userEntity.getId(), userEntity.getUsername());
        return result;
    }

    /**
     * 实体转DTO
     */
    private SysUserDTO convertToDTO(SysUserEntity entity) {
        SysUserDTO dto = new SysUserDTO();
        BeanUtils.copyProperties(entity, dto);
        
        // 获取用户角色
        List<SysRoleEntity> roles = roleRepository.findByUserId(entity.getId());
        if (!CollectionUtils.isEmpty(roles)) {
            dto.setRoleIds(roles.stream().map(SysRoleEntity::getId).collect(Collectors.toList()));
            dto.setRoleNames(roles.stream().map(SysRoleEntity::getRoleName).collect(Collectors.toList()));
        }
        
        return dto;
    }
}
