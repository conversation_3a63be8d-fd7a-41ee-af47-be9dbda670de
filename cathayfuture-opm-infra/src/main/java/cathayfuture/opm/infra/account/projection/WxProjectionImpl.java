package cathayfuture.opm.infra.account.projection;

import cathayfuture.opm.client.account.exception.WxLoginException;
import cathayfuture.opm.domain.account.AccountEntity;
import cathayfuture.opm.domain.account.projection.WxProjection;
import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date ********
 */
@Slf4j
@Component
public class WxProjectionImpl implements WxProjection {

    private static final int CODE_BEEN_USED_ERROR_CODE = 40163;

    @Resource
    private WxMaService wxMaService;

    @Override
    public AccountEntity getSessionInfo(String code) {
        log.info("获取微信会话信息，jsCode[{}]", code);
        try {
            AccountEntity accountEntity = new AccountEntity();
            WxMaJscode2SessionResult session = wxMaService.getUserService().getSessionInfo(code);
            accountEntity.setOpenId(session.getOpenid());
            accountEntity.setUnionId(session.getUnionid());
            return accountEntity;
        } catch (WxErrorException e) {
            if (Objects.equals(e.getError().getErrorCode(), CODE_BEEN_USED_ERROR_CODE)) {
                log.warn("获取微信会话信息异常，code[{}]", code, e);
            } else {
                log.error("获取微信会话信息异常，code[{}]", code, e);
            }
            throw new WxLoginException(e.getError().getErrorMsg());
        }
    }

    @Override
    public String getPhoneNumber(String code) {
        try {
            WxMaPhoneNumberInfo phoneNoInfo = wxMaService.getUserService().getNewPhoneNoInfo(code);
            return phoneNoInfo.getPhoneNumber();
        } catch (WxErrorException e) {
            log.error("获取微信会话信息异常，code[{}]", code, e);
        }
        return null;
    }


}
