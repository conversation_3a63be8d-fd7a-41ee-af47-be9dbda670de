2025-08-12 10:26:26,322 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-12 10:26:27,669 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-12 10:42:24,865 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-12 10:42:26,275 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-12 10:45:45,051 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:85] - Token验证成功: username=admin
2025-08-12 10:45:45,078 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.JwtAuthenticationFilter [L:61] - JWT认证成功，用户: admin, 权限: [ROLE_ADMIN]
2025-08-12 10:46:16,618 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-12 10:46:18,005 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-12 10:46:58,233 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-12 10:46:59,588 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-12 14:29:11,098 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-12 14:29:12,614 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-12 14:30:40,464 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: admin
2025-08-12 14:30:40,521 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:107] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-12 14:30:40,522 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 admin 加载完成，权限: [ROLE_ADMIN]
2025-08-12 14:30:40,615 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: manager
2025-08-12 14:30:40,622 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:107] - 为用户 2 添加角色权限: ROLE_MANAGER
2025-08-12 14:30:40,622 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 manager 加载完成，权限: [ROLE_MANAGER]
2025-08-12 14:30:40,704 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: admin
2025-08-12 14:30:40,710 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:107] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-12 14:30:40,710 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 admin 加载完成，权限: [ROLE_ADMIN]
2025-08-12 14:30:40,792 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: nonexistuser
2025-08-12 14:33:40,749 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: admin
2025-08-12 14:33:40,759 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:107] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-12 14:33:40,759 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 admin 加载完成，权限: [ROLE_ADMIN]
2025-08-12 14:33:40,845 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: manager
2025-08-12 14:33:40,853 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.DatabaseUserDetailsService [L:107] - 为用户 2 添加角色权限: ROLE_MANAGER
2025-08-12 14:33:40,853 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 manager 加载完成，权限: [ROLE_MANAGER]
2025-08-12 14:33:40,934 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: admin
2025-08-12 14:33:40,943 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:107] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-12 14:33:40,944 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 admin 加载完成，权限: [ROLE_ADMIN]
2025-08-12 14:33:41,025 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: nonexist
2025-08-12 14:33:57,695 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.c.e.handle.MyExceptionHandler [L:66] - -------field:code,message:不能为空
2025-08-12 14:34:20,374 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: admin
2025-08-12 14:34:20,386 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:107] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-12 14:34:20,386 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 admin 加载完成，权限: [ROLE_ADMIN]
2025-08-12 14:34:36,241 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: admin
2025-08-12 14:34:36,250 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.DatabaseUserDetailsService [L:107] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-12 14:34:36,251 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 admin 加载完成，权限: [ROLE_ADMIN]
2025-08-12 14:35:51,044 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: admin
2025-08-12 14:35:51,072 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:107] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-12 14:35:51,073 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 admin 加载完成，权限: [ROLE_ADMIN]
2025-08-12 14:35:58,409 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: admin
2025-08-12 14:35:58,426 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:107] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-12 14:35:58,426 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 admin 加载完成，权限: [ROLE_ADMIN]
2025-08-12 14:36:25,967 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: admin
2025-08-12 14:36:25,976 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:107] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-12 14:36:25,976 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 admin 加载完成，权限: [ROLE_ADMIN]
2025-08-12 14:36:25,980 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: manager
2025-08-12 14:36:25,987 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:107] - 为用户 2 添加角色权限: ROLE_MANAGER
2025-08-12 14:36:25,988 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 manager 加载完成，权限: [ROLE_MANAGER]
2025-08-12 14:37:18,121 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: testuser
2025-08-12 14:37:18,126 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:123] - 用户 3 没有分配角色，使用默认角色 ROLE_USER
2025-08-12 14:37:18,126 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 testuser 加载完成，权限: [ROLE_USER]
2025-08-12 14:37:18,239 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:56] - Token存储成功: username=testuser, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-12 14:37:18,241 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:188] - Refresh Token生成成功: username=testuser
2025-08-12 14:38:07,705 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: admin
2025-08-12 14:38:07,712 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:107] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-12 14:38:07,713 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 admin 加载完成，权限: [ROLE_ADMIN]
2025-08-12 14:38:08,825 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: manager
2025-08-12 14:38:08,833 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.DatabaseUserDetailsService [L:107] - 为用户 2 添加角色权限: ROLE_MANAGER
2025-08-12 14:38:08,833 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 manager 加载完成，权限: [ROLE_MANAGER]
2025-08-12 14:38:09,225 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: testuser
2025-08-12 14:38:09,231 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.DatabaseUserDetailsService [L:123] - 用户 3 没有分配角色，使用默认角色 ROLE_USER
2025-08-12 14:38:09,231 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 testuser 加载完成，权限: [ROLE_USER]
2025-08-12 14:38:09,301 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.TokenManagementService [L:56] - Token存储成功: username=testuser, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-12 14:38:09,304 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.TokenManagementService [L:188] - Refresh Token生成成功: username=testuser
2025-08-12 14:39:01,619 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: admin
2025-08-12 14:39:01,631 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:107] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-12 14:39:01,632 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 admin 加载完成，权限: [ROLE_ADMIN]
2025-08-12 14:39:02,879 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: admin
2025-08-12 14:39:02,889 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.DatabaseUserDetailsService [L:107] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-12 14:39:02,889 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 admin 加载完成，权限: [ROLE_ADMIN]
2025-08-12 14:39:03,592 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: admin
2025-08-12 14:39:03,599 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:107] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-12 14:39:03,599 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 admin 加载完成，权限: [ROLE_ADMIN]
2025-08-12 14:39:28,222 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: admin
2025-08-12 14:39:28,240 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:107] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-12 14:39:28,240 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 admin 加载完成，权限: [ROLE_ADMIN]
2025-08-12 14:39:29,066 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: admin
2025-08-12 14:39:29,076 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:107] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-12 14:39:29,077 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 admin 加载完成，权限: [ROLE_ADMIN]
2025-08-12 14:39:49,010 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: debuguser
2025-08-12 14:39:49,015 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.DatabaseUserDetailsService [L:123] - 用户 4 没有分配角色，使用默认角色 ROLE_USER
2025-08-12 14:39:49,015 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 debuguser 加载完成，权限: [ROLE_USER]
2025-08-12 14:39:49,089 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.TokenManagementService [L:56] - Token存储成功: username=debuguser, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-12 14:39:49,090 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.TokenManagementService [L:188] - Refresh Token生成成功: username=debuguser
2025-08-12 14:39:49,139 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: admin
2025-08-12 14:39:49,144 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:107] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-12 14:39:49,144 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 admin 加载完成，权限: [ROLE_ADMIN]
2025-08-12 14:40:29,892 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: admin
2025-08-12 14:40:29,906 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.DatabaseUserDetailsService [L:107] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-12 14:40:29,907 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 admin 加载完成，权限: [ROLE_ADMIN]
2025-08-12 14:40:30,537 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: admin
2025-08-12 14:40:30,545 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:107] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-12 14:40:30,545 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 admin 加载完成，权限: [ROLE_ADMIN]
2025-08-12 14:40:52,720 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: admin
2025-08-12 14:40:52,726 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.DatabaseUserDetailsService [L:107] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-12 14:40:52,727 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 admin 加载完成，权限: [ROLE_ADMIN]
2025-08-12 14:40:52,786 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: debuguser
2025-08-12 14:40:52,791 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:123] - 用户 4 没有分配角色，使用默认角色 ROLE_USER
2025-08-12 14:40:52,791 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 debuguser 加载完成，权限: [ROLE_USER]
2025-08-12 14:40:52,861 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.TokenManagementService [L:56] - Token存储成功: username=debuguser, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-12 14:40:52,862 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.TokenManagementService [L:188] - Refresh Token生成成功: username=debuguser
2025-08-12 14:40:53,086 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: manager
2025-08-12 14:40:53,092 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:107] - 为用户 2 添加角色权限: ROLE_MANAGER
2025-08-12 14:40:53,092 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 manager 加载完成，权限: [ROLE_MANAGER]
2025-08-12 14:40:53,151 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: testuser
2025-08-12 14:40:53,157 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:123] - 用户 3 没有分配角色，使用默认角色 ROLE_USER
2025-08-12 14:40:53,158 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 testuser 加载完成，权限: [ROLE_USER]
2025-08-12 14:40:53,226 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.TokenManagementService [L:56] - Token存储成功: username=testuser, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-12 14:40:53,228 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.TokenManagementService [L:188] - Refresh Token生成成功: username=testuser
2025-08-12 14:40:53,324 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: springtest
2025-08-12 14:40:53,329 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.DatabaseUserDetailsService [L:123] - 用户 5 没有分配角色，使用默认角色 ROLE_USER
2025-08-12 14:40:53,329 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 springtest 加载完成，权限: [ROLE_USER]
2025-08-12 14:40:53,398 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.TokenManagementService [L:56] - Token存储成功: username=springtest, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-12 14:40:53,399 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.TokenManagementService [L:188] - Refresh Token生成成功: username=springtest
2025-08-12 14:40:53,604 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: admin
2025-08-12 14:40:53,610 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:107] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-12 14:40:53,610 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 admin 加载完成，权限: [ROLE_ADMIN]
2025-08-12 14:41:13,819 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: admin
2025-08-12 14:41:13,833 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:107] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-12 14:41:13,834 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 admin 加载完成，权限: [ROLE_ADMIN]
2025-08-12 14:41:14,791 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: admin
2025-08-12 14:41:14,803 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:107] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-12 14:41:14,804 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 admin 加载完成，权限: [ROLE_ADMIN]
2025-08-12 14:41:17,719 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: admin
2025-08-12 14:41:17,727 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:107] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-12 14:41:17,728 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 admin 加载完成，权限: [ROLE_ADMIN]
2025-08-12 14:41:51,773 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: admin
2025-08-12 14:41:51,778 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:107] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-12 14:41:51,778 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 admin 加载完成，权限: [ROLE_ADMIN]
2025-08-12 14:41:51,972 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: manager
2025-08-12 14:41:51,979 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:107] - 为用户 2 添加角色权限: ROLE_MANAGER
2025-08-12 14:41:51,980 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 manager 加载完成，权限: [ROLE_MANAGER]
2025-08-12 14:41:52,006 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: admin
2025-08-12 14:41:52,011 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.DatabaseUserDetailsService [L:107] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-12 14:41:52,011 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 admin 加载完成，权限: [ROLE_ADMIN]
2025-08-12 14:41:52,013 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: manager
2025-08-12 14:41:52,017 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:107] - 为用户 2 添加角色权限: ROLE_MANAGER
2025-08-12 14:41:52,017 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 manager 加载完成，权限: [ROLE_MANAGER]
2025-08-12 14:41:52,020 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: testuser
2025-08-12 14:41:52,024 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:123] - 用户 3 没有分配角色，使用默认角色 ROLE_USER
2025-08-12 14:41:52,024 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 testuser 加载完成，权限: [ROLE_USER]
2025-08-12 14:41:52,094 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:56] - Token存储成功: username=testuser, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-12 14:41:52,095 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:188] - Refresh Token生成成功: username=testuser
2025-08-12 14:41:52,099 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: debuguser
2025-08-12 14:41:52,103 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:123] - 用户 4 没有分配角色，使用默认角色 ROLE_USER
2025-08-12 14:41:52,103 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 debuguser 加载完成，权限: [ROLE_USER]
2025-08-12 14:41:52,178 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.TokenManagementService [L:56] - Token存储成功: username=debuguser, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-12 14:41:52,185 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.TokenManagementService [L:188] - Refresh Token生成成功: username=debuguser
2025-08-12 14:42:58,599 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: admin
2025-08-12 14:42:58,613 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:107] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-12 14:42:58,615 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 admin 加载完成，权限: [ROLE_ADMIN]
2025-08-12 14:43:04,688 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: temp_admin
2025-08-12 14:43:04,692 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:123] - 用户 7 没有分配角色，使用默认角色 ROLE_USER
2025-08-12 14:43:04,693 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 temp_admin 加载完成，权限: [ROLE_USER]
2025-08-12 14:43:04,765 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: temp_manager
2025-08-12 14:43:04,770 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:123] - 用户 8 没有分配角色，使用默认角色 ROLE_USER
2025-08-12 14:43:04,770 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 temp_manager 加载完成，权限: [ROLE_USER]
2025-08-12 14:43:04,840 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: springtest
2025-08-12 14:43:04,847 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:123] - 用户 5 没有分配角色，使用默认角色 ROLE_USER
2025-08-12 14:43:04,847 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 springtest 加载完成，权限: [ROLE_USER]
2025-08-12 14:43:04,918 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: tempadmin
2025-08-12 14:43:04,923 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.DatabaseUserDetailsService [L:123] - 用户 6 没有分配角色，使用默认角色 ROLE_USER
2025-08-12 14:43:04,923 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 tempadmin 加载完成，权限: [ROLE_USER]
2025-08-12 14:43:04,993 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: debuguser
2025-08-12 14:43:05,000 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:123] - 用户 4 没有分配角色，使用默认角色 ROLE_USER
2025-08-12 14:43:05,001 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 debuguser 加载完成，权限: [ROLE_USER]
2025-08-12 14:43:05,070 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.TokenManagementService [L:56] - Token存储成功: username=debuguser, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-12 14:43:05,073 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.TokenManagementService [L:188] - Refresh Token生成成功: username=debuguser
2025-08-12 14:43:05,076 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: testuser
2025-08-12 14:43:05,081 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.DatabaseUserDetailsService [L:123] - 用户 3 没有分配角色，使用默认角色 ROLE_USER
2025-08-12 14:43:05,081 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 testuser 加载完成，权限: [ROLE_USER]
2025-08-12 14:43:05,151 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.TokenManagementService [L:56] - Token存储成功: username=testuser, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-12 14:43:05,153 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.TokenManagementService [L:188] - Refresh Token生成成功: username=testuser
2025-08-12 14:43:05,156 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: admin
2025-08-12 14:43:05,172 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:107] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-12 14:43:05,172 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 admin 加载完成，权限: [ROLE_ADMIN]
2025-08-12 14:43:05,176 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: manager
2025-08-12 14:43:05,181 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:107] - 为用户 2 添加角色权限: ROLE_MANAGER
2025-08-12 14:43:05,181 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 manager 加载完成，权限: [ROLE_MANAGER]
2025-08-12 14:43:05,313 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: adminclone
2025-08-12 14:43:05,320 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:107] - 为用户 9 添加角色权限: ROLE_ADMIN
2025-08-12 14:43:05,320 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 adminclone 加载完成，权限: [ROLE_ADMIN]
2025-08-12 14:43:05,389 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.TokenManagementService [L:56] - Token存储成功: username=adminclone, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-12 14:43:05,391 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.TokenManagementService [L:188] - Refresh Token生成成功: username=adminclone
2025-08-12 14:44:08,889 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: admin
2025-08-12 14:44:08,894 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:107] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-12 14:44:08,895 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 admin 加载完成，权限: [ROLE_ADMIN]
2025-08-12 14:44:08,898 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: manager
2025-08-12 14:44:08,902 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:107] - 为用户 2 添加角色权限: ROLE_MANAGER
2025-08-12 14:44:08,903 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 manager 加载完成，权限: [ROLE_MANAGER]
2025-08-12 14:44:08,905 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: testuser
2025-08-12 14:44:08,909 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:123] - 用户 3 没有分配角色，使用默认角色 ROLE_USER
2025-08-12 14:44:08,909 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 testuser 加载完成，权限: [ROLE_USER]
2025-08-12 14:44:08,980 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:56] - Token存储成功: username=testuser, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-12 14:44:08,982 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:188] - Refresh Token生成成功: username=testuser
2025-08-12 14:44:08,997 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.TokenManagementService [L:85] - Token验证成功: username=testuser
2025-08-12 14:44:09,005 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.JwtAuthenticationFilter [L:61] - JWT认证成功，用户: testuser, 权限: [ROLE_USER]
2025-08-12 14:44:09,026 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: debuguser
2025-08-12 14:44:09,030 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:123] - 用户 4 没有分配角色，使用默认角色 ROLE_USER
2025-08-12 14:44:09,031 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 debuguser 加载完成，权限: [ROLE_USER]
2025-08-12 14:44:09,101 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.TokenManagementService [L:56] - Token存储成功: username=debuguser, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-12 14:44:09,103 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.TokenManagementService [L:188] - Refresh Token生成成功: username=debuguser
2025-08-12 14:44:09,111 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.TokenManagementService [L:85] - Token验证成功: username=debuguser
2025-08-12 14:44:09,112 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.JwtAuthenticationFilter [L:61] - JWT认证成功，用户: debuguser, 权限: [ROLE_USER]
2025-08-12 14:44:09,135 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:44] - 开始加载用户信息: adminclone
2025-08-12 14:44:09,140 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:107] - 为用户 9 添加角色权限: ROLE_ADMIN
2025-08-12 14:44:09,140 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:74] - 用户 adminclone 加载完成，权限: [ROLE_ADMIN]
2025-08-12 14:44:09,208 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.TokenManagementService [L:56] - Token存储成功: username=adminclone, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-12 14:44:09,210 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.TokenManagementService [L:188] - Refresh Token生成成功: username=adminclone
2025-08-12 14:44:09,216 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.TokenManagementService [L:85] - Token验证成功: username=adminclone
2025-08-12 14:44:09,216 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.JwtAuthenticationFilter [L:61] - JWT认证成功，用户: adminclone, 权限: [ROLE_ADMIN]
2025-08-12 14:56:29,388 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-12 14:56:30,806 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-12 14:56:57,207 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-12 14:56:58,617 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-12 14:59:11,441 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-12 14:59:12,811 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-12 14:59:33,095 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:140] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-12 14:59:33,127 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:156] - 用户 3 没有分配角色，使用默认角色 ROLE_USER
2025-08-12 14:59:33,241 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.TokenManagementService [L:56] - Token存储成功: username=testuser, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-12 14:59:33,244 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.TokenManagementService [L:188] - Refresh Token生成成功: username=testuser
2025-08-12 15:01:21,340 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:140] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-12 15:01:22,580 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.DatabaseUserDetailsService [L:140] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-12 15:04:27,349 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-12 15:04:28,804 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-12 15:04:33,697 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:141] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-12 15:04:33,817 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.TokenManagementService [L:56] - Token存储成功: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-12 15:04:33,819 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.TokenManagementService [L:188] - Refresh Token生成成功: username=admin
2025-08-12 15:07:47,477 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-12 15:07:48,796 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-12 15:12:40,598 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-12 15:12:41,906 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-12 15:24:49,586 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-12 15:24:50,913 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-12 15:25:56,964 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-12 15:25:58,280 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-12 15:36:52,055 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-12 15:36:53,397 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-12 15:38:52,014 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-12 15:38:53,335 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-12 15:39:14,847 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-12 15:39:16,180 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
